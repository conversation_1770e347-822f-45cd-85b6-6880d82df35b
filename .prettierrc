{"arrowParens": "always", "bracketSameLine": true, "bracketSpacing": true, "semi": true, "singleQuote": false, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "printWidth": 200, "requirePragma": false, "tabWidth": 3, "useTabs": false, "embeddedLanguageFormatting": "auto"}