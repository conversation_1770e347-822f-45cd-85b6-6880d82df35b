<!doctype html>
<html lang="en">

<head>
   @@include("partials/head.html") @@include("partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Billing - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Billing - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start billing page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("partials/navbar-vertical.html", { "page": "billing", "page_group": "pages" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("partials/top-navbar.html")
            <!-- start billing section -->
            <div class="p-6">
               <!-- title -->
               <div class="flex items-center mb-4 border-b border-gray-300 pb-4">
                  <h1 class="inline-block text-xl font-semibold leading-6">Billing</h1>
               </div>

               <div class="mb-8 flex items-center">
                  <!-- card -->
                  <div class="card shadow lg:w-[1200px] mx-auto w-full">
                     <div class="border-b border-gray-300 px-5 py-4 flex justify-between items-center">
                        <h4 class="font-semibold">Current Plan Overview</h4>
                     </div>
                     <!-- card body -->
                     <div class="card-body">
                        <!-- row -->
                        <div class="grid md:grid-cols-2 grid-rows-1 grid-flow-row-dense gap-6">
                           <div class="col-span-1">
                              <div class="mb-2">
                                 <!-- content-->
                                 <p class="mb-0">Current Plan</p>
                                 <h3 class="mt-2 mb-3 font-bold">Starter - Jan 2021</h3>
                                 <p>Unlimited access to essential tools for design, bootstrap themes, illustrator and
                                    icons.</p>
                                 <p class="mt-4 flex items-center gap-1">
                                    <span><i data-feather="info" class="w-4 h-4 mr-2"></i></span>
                                    <span>Next Payment: on</span>
                                    <span class="text-indigo-600">$499.00 USD</span>
                                    <span class="text-gray-900 font-bold">Jan 1, 2022</span>
                                 </p>
                              </div>
                           </div>
                           <!-- col  -->
                           <div class="md:flex md:justify-end">
                              <!-- content  -->
                              <div>
                                 <span>Yearly Payment</span>
                                 <h2 class="text-indigo-600 font-bold text-2xl">$499 USD</h2>
                                 <a href="#" class="mb-6 block d-block">Learn more about our membership policy</a>
                                 <div class="grid gap-2 text-center">
                                    <a href="#"
                                       class="btn gap-x-2 bg-gray-800 text-white border-gray-800 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-900 hover:border-gray-900 active:bg-gray-900 active:border-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300"
                                       data-bs-toggle="modal" data-bs-target="#planModal">
                                       Change Plan
                                    </a>
                                    <a href="#"
                                       class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                       Cancel Subscription
                                    </a>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="border-t border-gray-300 px-6 py-4 bg-white rounded-b-xl">
                        <div class="flex flex-col md:flex-row gap-5 justify-between items-center">
                           <!-- text -->
                           <div class="md:mb-3 lg:mb-0 text-center sm:text-left">
                              <h5 class="uppercase mb-0">Payment method</h5>
                              <div class="mt-2 flex items-center justify-center gap-3">
                                 <img src="assets/images/creditcard/mastercard.svg" alt="" />
                                 <span>***8773</span>
                              </div>
                           </div>
                           <div class="text-center md:text-left">
                              <a href="#" class="text-red-500 mr-4">Remove</a>
                              <a href="#"
                                 class="btn gap-x-2 bg-white text-gray-800 border-gray-300 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700 active:border-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                 Change Card
                              </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-8 flex items-center">
                  <!-- card -->
                  <div class="card shadow lg:w-[1200px] mx-auto w-full">
                     <div class="border-b border-gray-300 px-5 py-4 flex justify-between items-center">
                        <h4>Billing Address</h4>
                     </div>
                     <!-- card body -->
                     <div class="card-body">
                        <div
                           class="grid lg:grid-cols-2 grid-rows-1 grid-flow-row-dense gap-6 border-b border-gray-300 mb-5 pb-5">
                           <div class="col-span-1">
                              <div class="mb-3 mb-lg-0">
                                 <!-- radio -->
                                 <div>
                                    <input type="radio" id="shippingBillingAddress" name="customRadio"
                                       class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded-full focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                       checked="" />
                                    <label for="shippingBillingAddress" class="ml-2 text-gray-800 font-bold">Shipping
                                       Billing Address</label>
                                    <div class="ml-7 block">
                                       <h4 class="mt-3 mb-1">Valarie Tarrant</h4>
                                       <span class="block">3757 Morgan Street Tallahassee, FL 32301</span>
                                       <div class="flex flex-row gap-4 mt-4">
                                          <a href="#" class="text-green-500">Edit</a>
                                          <a href="#" class="text-red-500">Delete</a>
                                          <a href="#" class=" ">Remove as Default Billing</a>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="flex lg:justify-end">
                              <!-- text -->
                              <div class="mb-2">
                                 <p class="mb-1">
                                    E-mail:
                                    <a href="#" class="text-indigo-600"><EMAIL></a>
                                 </p>
                                 <p>Phone: ************</p>
                              </div>
                           </div>
                        </div>
                        <div
                           class="grid lg:grid-cols-2 grid-rows-1 grid-flow-row-dense gap-6 border-b border-gray-300 mb-5 pb-5">
                           <div class="col-span-1">
                              <!-- radio -->
                              <div>
                                 <input type="radio" id="customRadio2"
                                    class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded-full focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                    name="customRadio" />
                                 <label for="customRadio2" class="ml-2 text-gray-800 font-bold">Default Billing
                                    Address</label>
                                 <div class="ml-7 block">
                                    <h4 class="mt-3 mb-1">Mildred Cantu</h4>
                                    <span class="block">3757 Morgan Street Tallahassee, FL 32301</span>
                                    <div class="flex flex-row gap-4 mt-4">
                                       <a href="#" class="text-green-500">Edit</a>
                                       <a href="#" class="text-red-500">Delete</a>
                                       <a href="#" class=" ">Set as Default</a>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="flex lg:justify-end">
                              <!-- text -->
                              <div class="mb-2">
                                 <p class="mb-1">
                                    E-mail:
                                    <a href="#" class="text-indigo-600"><EMAIL></a>
                                 </p>
                                 <p>Phone: ************</p>
                              </div>
                           </div>
                        </div>
                        <div class="mt-5 flex">
                           <!-- button -->
                           <a href="#"
                              class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                              data-bs-toggle="modal" data-bs-target="#billingAddressModal">
                              Add New Address
                           </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <!-- end of billing section -->
            @@include("partials/footer.html")
         </div>
      </div>
      <!-- end of billing page -->
   </main>

   @@include("partials/buy-template.html")
   @@include("partials/scripts.html")
</body>

</html>