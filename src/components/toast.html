<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Toast - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Toast - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start toast page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "toast", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Toast</h1>
                  <p class="text-lg">Push notifications to your visitors with a toast, a lightweight and easily
                     customizable alert message.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <!-- toast start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Basic Example</h2>
                        <p>
                           To encourage extensible and predictable toasts, we recommend a header and body. Toast headers
                           use
                           <code class="text-red-600">display:flex</code>
                           , allowing easy alignment of content thanks to our margin and flexbox utilities.
                        </p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-basic-toast-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-basic-toast" type="button" role="tab"
                                 aria-controls="pills-design-basic-toast" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-basic-toast-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-basic-toast" type="button" role="tab"
                                 aria-controls="pills-html-basic-toast" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-basic-toast" role="tabpanel"
                              aria-labelledby="pills-design-basic-toast-tab" tabindex="0">
                              <div aria-live="assertive" aria-atomic="true"
                                 class="toast fade show border border-gray-300 flex flex-col w-full max-w-xs text-gray-500 bg-white rounded-lg"
                                 role="alert">
                                 <div class="flex items-center w-full border-b border-gray-300 p-3">
                                    <h4 class="text-gray-800">Tailwind CSS</h4>
                                    <button type="button"
                                       class="btn-close ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8"
                                       data-bs-dismiss="toast" aria-label="Close">
                                       <span class="sr-only">Close</span>
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                    </button>
                                 </div>
                                 <div class="p-3">
                                    <p>Hello, world! This is a toast message.</p>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-basic-toast" role="tabpanel"
                              aria-labelledby="pills-html-basic-toast-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">aria-live</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>assertive<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-atomic</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>toast fade show border border-gray-300
                                          flex flex-col w-full max-w-xs text-gray-500 bg-white rounded-lg <span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center w-full border-b
                                          border-gray-300 p-3<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h4</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-800<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Tailwind CSS<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h4</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close ms-auto -mx-1.5 -my-1.5
                                          bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2
                                          focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center
                                          justify-center h-8 w-8 <span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>toast<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>p</span><span
                                          class="token punctuation">&gt;</span></span>Hello, world! This is a toast
                                    message.<span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>p</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- toast end -->
                  <!-- toast start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Live Toast</h2>
                        <p>Click the button below to show a toast (positioned with our utilities in the lower right
                           corner) that has been hidden by default.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-live-toast-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-live-toast" type="button" role="tab"
                                 aria-controls="pills-design-live-toast" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-live-toast-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-live-toast" type="button" role="tab"
                                 aria-controls="pills-html-live-toast" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-live-toast" role="tabpanel"
                              aria-labelledby="pills-design-live-toast-tab" tabindex="0">
                              <button type="button"
                                 class="btn bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 id="liveToastBtn">
                                 Show live toast
                              </button>
                              <div class="toast-container fixed top-0 right-0 p-3">
                                 <div id="liveToast" aria-live="assertive" aria-atomic="true"
                                    class="toast border border-gray-300 flex flex-col w-full max-w-xs text-gray-500 bg-white rounded-lg"
                                    role="alert">
                                    <div class="flex items-center w-full border-b border-gray-300 p-3">
                                       <h4 class="text-gray-800">Tailwind CSS</h4>
                                       <button type="button"
                                          class="btn-close ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8"
                                          data-bs-dismiss="toast" aria-label="Close">
                                          <span class="sr-only">Close</span>
                                          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 14 14">
                                             <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                          </svg>
                                       </button>
                                    </div>
                                    <div class="p-3">
                                       <p>Hello, world! This is a toast message.</p>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-live-toast" role="tabpanel"
                              aria-labelledby="pills-html-live-toast-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"> <span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>liveToastBtn<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Show live toast
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>toast-container fixed top-0 right-0
                                          p-3<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>liveToast<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-live</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>assertive<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-atomic</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>toast border border-gray-300 flex
                                          flex-col w-full max-w-xs text-gray-500 bg-white rounded-lg <span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center w-full border-b
                                          border-gray-300 p-3<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h4</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-800<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Tailwind CSS<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h4</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close ms-auto -mx-1.5 -my-1.5
                                          bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2
                                          focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center
                                          justify-center h-8 w-8 <span class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>toast<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>p</span><span
                                          class="token punctuation">&gt;</span></span>Hello, world! This is a toast
                                    message.<span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>p</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- toast end -->
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end toast page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>