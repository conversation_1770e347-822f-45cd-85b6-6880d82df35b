<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Badge - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Badge - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start badge page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "badge", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Badge</h1>
                  <p class="text-lg">A visual indicator for a value or status descriptor for UI elements.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Solid color variants</h2>
                        <p>The default form of solid color badges.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active" id="pills-design-tab"
                                 data-bs-toggle="pill" data-bs-target="#pills-design" type="button" role="tab"
                                 aria-controls="pills-design" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-tab" data-bs-toggle="pill" data-bs-target="#pills-html" type="button"
                                 role="tab" aria-controls="pills-html" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design" role="tabpanel"
                              aria-labelledby="pills-design-tab" tabindex="0">
                              <span
                                 class="bg-indigo-600 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-500 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-teal-500 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-blue-500 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-red-500 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-yellow-500 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-800 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-300 px-2 py-1 text-white text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                           </div>
                           <div class="tab-pane fade" id="pills-html" role="tabpanel" aria-labelledby="pills-html-tab"
                              tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"> <span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-600 px-2 py-1 text-white
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-teal-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-blue-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-red-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-yellow-500 px-2 py-1 text-white
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-800 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-300 px-2 py-1 text-white text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>

                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Rounded Solid color variants</h2>
                        <p>The default form of solid color badges With Rounded.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-rounded-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-rounded" type="button" role="tab"
                                 aria-controls="pills-design-rounded" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-rounded-tab" data-bs-toggle="pill" data-bs-target="#pills-html-rounded"
                                 type="button" role="tab" aria-controls="pills-html-rounded" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-rounded" role="tabpanel"
                              aria-labelledby="pills-design-rounded-tab" tabindex="0">
                              <span
                                 class="bg-indigo-600 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-500 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-teal-500 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-blue-500 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-red-500 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-yellow-500 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-800 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-300 px-2 py-1 text-white text-sm font-medium rounded-full inline-block whitespace-nowrap text-center">Badge</span>
                           </div>
                           <div class="tab-pane fade" id="pills-html-rounded" role="tabpanel"
                              aria-labelledby="pills-html-rounded-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"> <span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-600 px-2 py-1 text-white
                                          text-sm font-medium rounded-full inline-block whitespace-nowrap
                                          text-center<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-teal-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-blue-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-red-500 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-yellow-500 px-2 py-1 text-white
                                          text-sm font-medium rounded-full inline-block whitespace-nowrap
                                          text-center<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-800 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-300 px-2 py-1 text-white text-sm
                                          font-medium rounded-full inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Soft color variants</h2>
                        <p>The default form of soft color badges.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabThird" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-soft-rounded-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-soft-rounded" type="button" role="tab"
                                 aria-controls="pills-design-soft-rounded" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-soft-rounded-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-soft-rounded" type="button" role="tab"
                                 aria-controls="pills-html-soft-rounded" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabThirdContent">
                           <div class="tab-pane fade show active" id="pills-design-soft-rounded" role="tabpanel"
                              aria-labelledby="pills-design-soft-rounded-tab" tabindex="0">
                              <span
                                 class="bg-indigo-200 px-2 py-1 text-indigo-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-200 px-2 py-1 text-gray-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-teal-200 px-2 py-1 text-teal-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-blue-200 px-2 py-1 text-blue-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-red-200 px-2 py-1 text-red-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-yellow-200 px-2 py-1 text-yellow-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-300 px-2 py-1 text-gray-900 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                              <span
                                 class="bg-gray-200 px-2 py-1 text-gray-700 text-sm font-medium rounded-md inline-block whitespace-nowrap text-center">Badge</span>
                           </div>
                           <div class="tab-pane fade" id="pills-html-soft-rounded" role="tabpanel"
                              aria-labelledby="pills-html-soft-rounded-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-200 px-2 py-1 text-indigo-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-200 px-2 py-1 text-gray-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-teal-200 px-2 py-1 text-teal-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-blue-200 px-2 py-1 text-blue-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-red-200 px-2 py-1 text-red-700 text-sm
                                          font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-yellow-200 px-2 py-1 text-yellow-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-300 px-2 py-1 text-gray-900
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-200 px-2 py-1 text-gray-700
                                          text-sm font-medium rounded-md inline-block whitespace-nowrap text-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Badge<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
      <!-- end badge page -->
   </main>

   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>