<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Offcanvas - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Offcanvas - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start offcanvas page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "offcanvas", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Offcanvas</h1>
                  <p class="text-lg">
                     Offcanvas component, often referred to as a Drawer, offers a hidden sidebar solution for various
                     applications such as navigation menus, shopping carts, and more, enhancing the
                     user interface while maximizing space.
                  </p>
               </div>
               <div class="flex flex-col gap-8">
                  <!-- navbar start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Example</h2>
                        <p>Offcanvas start from Left Side</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-offcanvasLeft-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-offcanvasLeft" type="button" role="tab"
                                 aria-controls="pills-design-offcanvasLeft" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-offcanvasLeft-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-offcanvasLeft" type="button" role="tab"
                                 aria-controls="pills-html-offcanvasLeft" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-offcanvasLeft" role="tabpanel"
                              aria-labelledby="pills-design-offcanvasLeft-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample"
                                 aria-controls="offcanvasExample">
                                 Offcanvas Start
                              </button>

                              <div
                                 class="offcanvas -translate-x-full fixed top-0 start-0 border-r border-gray-300 transition-all duration-300 transform h-full invisible bg-white z-50 max-w-xs"
                                 tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
                                 <div class="flex items-center p-4">
                                    <h5 class="text-lg" id="offcanvasExampleLabel">Offcanvas</h5>
                                    <button type="button" class="btn-close"></button>

                                    <button type="button" data-bs-dismiss="offcanvas" aria-label="Close"
                                       class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center">
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                       <span class="sr-only">Close menu</span>
                                    </button>
                                 </div>
                                 <div class="p-4">
                                    <div>Some text as placeholder. In real life you can have the elements you have
                                       chosen. Like, text, images, lists, etc.</div>
                                    <div class="dropdown mt-3">
                                       <button class="btn btn-secondary dropdown-toggle" type="button"
                                          data-bs-toggle="dropdown">Dropdown button</button>
                                       <ul class="dropdown-menu">
                                          <li><a class="dropdown-item" href="#">Action</a></li>
                                          <li><a class="dropdown-item" href="#">Another action</a></li>
                                          <li><a class="dropdown-item" href="#">Something else here</a></li>
                                       </ul>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-offcanvasLeft" role="tabpanel"
                              aria-labelledby="pills-html-offcanvasLeft-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#offcanvasExample<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasExample<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Offcanvas Start
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas -translate-x-full fixed top-0
                                          start-0 border-r border-gray-300 transition-all duration-300 transform h-full
                                          invisible bg-white z-50 max-w-xs<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>-1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasExample<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasExampleLabel<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h5</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-lg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasExampleLabel<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Offcanvas<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h5</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-400 bg-transparent
                                          hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute
                                          top-2.5 end-2.5 flex items-center justify-center <span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close menu<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span><span
                                          class="token punctuation">&gt;</span></span>Some text as placeholder. In real
                                    life you can have the elements you have chosen. Like, text, images, lists, etc.<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown mt-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-secondary dropdown-toggle<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dropdown button<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Right Offcanvas</h2>
                        <p>Offcanvas start from Right Side</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-offcanvasRight-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-offcanvasRight" type="button" role="tab"
                                 aria-controls="pills-design-offcanvasRight" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-offcanvasRight-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-offcanvasRight" type="button" role="tab"
                                 aria-controls="pills-html-offcanvasRight" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-offcanvasRight" role="tabpanel"
                              aria-labelledby="pills-design-offcanvasRight-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight"
                                 aria-controls="offcanvasRight">
                                 Offcanvas Right
                              </button>

                              <div
                                 class="offcanvas translate-x-full fixed top-0 right-0 border-l border-gray-300 transition-all duration-300 transform h-full invisible bg-white z-50 max-w-xs"
                                 tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel">
                                 <div class="flex items-center p-4">
                                    <h5 class="text-lg" id="offcanvasRightLabel">Offcanvas</h5>
                                    <button type="button" class="btn-close"></button>

                                    <button type="button" data-bs-dismiss="offcanvas" aria-label="Close"
                                       class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center">
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                       <span class="sr-only">Close menu</span>
                                    </button>
                                 </div>
                                 <div class="p-4">
                                    <div>Some text as placeholder. In real life you can have the elements you have
                                       chosen. Like, text, images, lists, etc.</div>
                                    <div class="dropdown mt-3">
                                       <button class="btn btn-secondary dropdown-toggle" type="button"
                                          data-bs-toggle="dropdown">Dropdown button</button>
                                       <ul class="dropdown-menu">
                                          <li><a class="dropdown-item" href="#">Action</a></li>
                                          <li><a class="dropdown-item" href="#">Another action</a></li>
                                          <li><a class="dropdown-item" href="#">Something else here</a></li>
                                       </ul>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-offcanvasRight" role="tabpanel"
                              aria-labelledby="pills-html-offcanvasRight-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#offcanvasRight<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasRight<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Offcanvas Right
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas translate-x-full fixed top-0
                                          right-0 border-l border-gray-300 transition-all duration-300 transform h-full
                                          invisible bg-white z-50 max-w-xs<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>-1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasRight<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasRightLabel<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h5</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-lg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasRightLabel<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Offcanvas<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h5</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-400 bg-transparent
                                          hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute
                                          top-2.5 end-2.5 flex items-center justify-center <span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close menu<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span><span
                                          class="token punctuation">&gt;</span></span>Some text as placeholder. In real
                                    life you can have the elements you have chosen. Like, text, images, lists, etc.<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown mt-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-secondary dropdown-toggle<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dropdown button<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Bottom Offcanvas</h2>
                        <p>Offcanvas start from Bottom Side</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabThird" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-offcanvasBottom-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-offcanvasBottom" type="button" role="tab"
                                 aria-controls="pills-design-offcanvasBottom" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-offcanvasBottom-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-offcanvasBottom" type="button" role="tab"
                                 aria-controls="pills-html-offcanvasBottom" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabThirdContent">
                           <div class="tab-pane fade show active" id="pills-design-offcanvasBottom" role="tabpanel"
                              aria-labelledby="pills-design-offcanvasBottom-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom"
                                 aria-controls="offcanvasBottom">
                                 Offcanvas Bottom
                              </button>

                              <div
                                 class="offcanvas translate-y-full fixed bottom-0 border-t right-0 left-0 border-gray-300 transition-all duration-300 w-full invisible bg-white z-50 max-h-xs"
                                 tabindex="-1" id="offcanvasBottom" aria-labelledby="offcanvasBottomLabel">
                                 <div class="flex items-center p-4">
                                    <h5 class="text-lg" id="offcanvasBottomLabel">Offcanvas</h5>
                                    <button type="button" class="btn-close"></button>

                                    <button type="button" data-bs-dismiss="offcanvas" aria-label="Close"
                                       class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center">
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                       <span class="sr-only">Close menu</span>
                                    </button>
                                 </div>
                                 <div class="p-4">
                                    <div>Some text as placeholder. In real life you can have the elements you have
                                       chosen. Like, text, images, lists, etc.</div>
                                    <div class="dropdown mt-3">
                                       <button class="btn btn-secondary dropdown-toggle" type="button"
                                          data-bs-toggle="dropdown">Dropdown button</button>
                                       <ul class="dropdown-menu">
                                          <li><a class="dropdown-item" href="#">Action</a></li>
                                          <li><a class="dropdown-item" href="#">Another action</a></li>
                                          <li><a class="dropdown-item" href="#">Something else here</a></li>
                                       </ul>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-offcanvasBottom" role="tabpanel"
                              aria-labelledby="pills-html-offcanvasBottom-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#offcanvasBottom<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBottom<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Offcanvas Bottom
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas translate-y-full fixed bottom-0
                                          border-t right-0 left-0 border-gray-300 transition-all duration-300 w-full
                                          invisible bg-white z-50 max-h-xs<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>-1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBottom<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBottomLabel<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h5</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-lg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBottomLabel<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Offcanvas<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h5</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-400 bg-transparent
                                          hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute
                                          top-2.5 end-2.5 flex items-center justify-center <span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close menu<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span><span
                                          class="token punctuation">&gt;</span></span>Some text as placeholder. In real
                                    life you can have the elements you have chosen. Like, text, images, lists, etc.<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown mt-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-secondary dropdown-toggle<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dropdown button<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>

                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Top Offcanvas</h2>
                        <p>Offcanvas start from Top Side</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabFourth" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-offcanvasTop-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-offcanvasTop" type="button" role="tab"
                                 aria-controls="pills-design-offcanvasTop" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-offcanvasTop-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-offcanvasTop" type="button" role="tab"
                                 aria-controls="pills-html-offcanvasTop" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabFourthContent">
                           <div class="tab-pane fade show active" id="pills-design-offcanvasTop" role="tabpanel"
                              aria-labelledby="pills-design-offcanvasTop-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 data-bs-toggle="offcanvas" data-bs-target="#offcanvasTop" aria-controls="offcanvasTop">
                                 Offcanvas Top
                              </button>

                              <div
                                 class="offcanvas -translate-y-full fixed top-0 border-t right-0 left-0 border-gray-300 transition-all duration-300 w-full invisible bg-white z-50 max-h-xs"
                                 tabindex="-1" id="offcanvasTop" aria-labelledby="offcanvasTopLabel">
                                 <div class="flex items-center p-4">
                                    <h5 class="text-lg" id="offcanvasTopLabel">Offcanvas</h5>
                                    <button type="button" class="btn-close"></button>

                                    <button type="button" data-bs-dismiss="offcanvas" aria-label="Close"
                                       class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center">
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                       <span class="sr-only">Close menu</span>
                                    </button>
                                 </div>
                                 <div class="p-4">
                                    <div>Some text as placeholder. In real life you can have the elements you have
                                       chosen. Like, text, images, lists, etc.</div>
                                    <div class="dropdown mt-3">
                                       <button class="btn btn-secondary dropdown-toggle" type="button"
                                          data-bs-toggle="dropdown">Dropdown button</button>
                                       <ul class="dropdown-menu">
                                          <li><a class="dropdown-item" href="#">Action</a></li>
                                          <li><a class="dropdown-item" href="#">Another action</a></li>
                                          <li><a class="dropdown-item" href="#">Something else here</a></li>
                                       </ul>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-offcanvasTop" role="tabpanel"
                              aria-labelledby="pills-html-offcanvasTop-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#offcanvasTop<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasTop<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Offcanvas Top
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas -translate-y-full fixed top-0
                                          border-t right-0 left-0 border-gray-300 transition-all duration-300 w-full
                                          invisible bg-white z-50 max-h-xs<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>-1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasTop<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasTopLabel<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h5</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-lg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasTopLabel<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Offcanvas<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h5</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-400 bg-transparent
                                          hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute
                                          top-2.5 end-2.5 flex items-center justify-center <span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close menu<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span><span
                                          class="token punctuation">&gt;</span></span>Some text as placeholder. In real
                                    life you can have the elements you have chosen. Like, text, images, lists, etc.<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown mt-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-secondary dropdown-toggle<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dropdown button<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Body scrolling</h2>
                        <p>Scrolling the &lt;body&gt; element is disabled when an offcanvas and its backdrop are
                           visible. Use the data-bs-scroll attribute to enable &lt;body&lt; scrolling.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabFifth" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-offcanvasBodyScroll-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-offcanvasBodyScroll" type="button" role="tab"
                                 aria-controls="pills-design-offcanvasBodyScroll" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-offcanvasBodyScroll-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-offcanvasBodyScroll" type="button" role="tab"
                                 aria-controls="pills-html-offcanvasBodyScroll" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabFifthContent">
                           <div class="tab-pane fade show active" id="pills-design-offcanvasBodyScroll" role="tabpanel"
                              aria-labelledby="pills-design-offcanvasBodyScroll-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                 data-bs-toggle="offcanvas" data-bs-target="#offcanvasBodyScroll"
                                 aria-controls="offcanvasBodyScroll">
                                 Offcanvas Start
                              </button>

                              <div
                                 class="offcanvas -translate-x-full fixed top-0 start-0 border-r border-gray-300 transition-all duration-300 transform h-full invisible bg-white z-50 max-w-xs"
                                 data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasBodyScroll"
                                 aria-labelledby="offcanvasBodyScrollLabel">
                                 <div class="flex items-center p-4">
                                    <h5 class="text-lg" id="offcanvasBodyScrollLabel">Offcanvas</h5>
                                    <button type="button" class="btn-close"></button>

                                    <button type="button" data-bs-dismiss="offcanvas" aria-label="Close"
                                       class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 flex items-center justify-center">
                                       <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                          fill="none" viewBox="0 0 14 14">
                                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                             stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                       </svg>
                                       <span class="sr-only">Close menu</span>
                                    </button>
                                 </div>
                                 <div class="p-4">
                                    <div>Some text as placeholder. In real life you can have the elements you have
                                       chosen. Like, text, images, lists, etc.</div>
                                    <div class="dropdown mt-3">
                                       <button class="btn btn-secondary dropdown-toggle" type="button"
                                          data-bs-toggle="dropdown">Dropdown button</button>
                                       <ul class="dropdown-menu">
                                          <li><a class="dropdown-item" href="#">Action</a></li>
                                          <li><a class="dropdown-item" href="#">Another action</a></li>
                                          <li><a class="dropdown-item" href="#">Something else here</a></li>
                                       </ul>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-offcanvasBodyScroll" role="tabpanel"
                              aria-labelledby="pills-html-offcanvasBodyScroll-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#offcanvasBodyScroll<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBodyScroll<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Offcanvas Start
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas -translate-x-full fixed top-0
                                          start-0 border-r border-gray-300 transition-all duration-300 transform h-full
                                          invisible bg-white z-50 max-w-xs<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-scroll</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-backdrop</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>false<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>-1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBodyScroll<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBodyScrollLabel<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>h5</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-lg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvasBodyScrollLabel<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Offcanvas<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>h5</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn-close<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-dismiss</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>offcanvas<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-label</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Close<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-gray-400 bg-transparent
                                          hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute
                                          top-2.5 end-2.5 flex items-center justify-center <span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-3 h-3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 14 14<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>sr-only<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Close menu<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>p-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span><span
                                          class="token punctuation">&gt;</span></span>Some text as placeholder. In real
                                    life you can have the elements you have chosen. Like, text, images, lists, etc.<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown mt-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-secondary dropdown-toggle<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dropdown button<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end offcanvas page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>