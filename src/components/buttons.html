<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Buttons - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Buttons - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start buttons page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "buttons", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Buttons</h1>
                  <p class="text-lg">Use different button styles for actions in forms, dialogs, and more with support
                     for multiple sizes, states, and more.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Solid color variants</h2>
                        <p>Predefined solid color button styles.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active" id="pills-design-tab"
                                 data-bs-toggle="pill" data-bs-target="#pills-design" type="button" role="tab"
                                 aria-controls="pills-design" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-tab" data-bs-toggle="pill" data-bs-target="#pills-html" type="button"
                                 role="tab" aria-controls="pills-html" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design" role="tabpanel"
                              aria-labelledby="pills-design-tab" tabindex="0">
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-gray-400 text-gray-800 border-gray-400 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-600 hover:border-gray-600 active:bg-gray-600 active:border-gray-600 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-teal-600 text-white border-teal-600 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-teal-700 hover:border-teal-700 active:bg-teal-700 active:border-teal-700 focus:outline-none focus:ring-4 focus:ring-teal-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-blue-700 text-white border-blue-700 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-blue-700 hover:border-blue-700 active:bg-blue-700 active:border-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-red-600 text-white border-red-600 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-red-700 hover:border-red-700 active:bg-red-700 active:border-red-700 focus:outline-none focus:ring-4 focus:ring-red-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-yellow-600 text-white border-yellow-600 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-yellow-700 hover:border-yellow-700 active:bg-yellow-700 active:border-yellow-700 focus:outline-none focus:ring-4 focus:ring-yellow-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-gray-800 text-white border-gray-800 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-900 hover:border-gray-900 active:bg-gray-900 active:border-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-gray-300 text-white border-gray-300 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700 active:border-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-white text-gray-800 border-gray-300 border disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700 active:border-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                 Button
                              </button>
                           </div>
                           <div class="tab-pane fade" id="pills-html" role="tabpanel" aria-labelledby="pills-html-tab"
                              tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-gray-400 text-gray-800
                                          border-gray-400 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-gray-600 hover:border-gray-600 active:bg-gray-600
                                          active:border-gray-600 focus:outline-none focus:ring-4
                                          focus:ring-gray-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-teal-600 text-white
                                          border-teal-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-teal-700 hover:border-teal-700 active:bg-teal-700
                                          active:border-teal-700 focus:outline-none focus:ring-4
                                          focus:ring-teal-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-blue-700 text-white
                                          border-blue-700 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-blue-700 hover:border-blue-700 active:bg-blue-700
                                          active:border-blue-700 focus:outline-none focus:ring-4
                                          focus:ring-blue-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-red-600 text-white
                                          border-red-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-red-700 hover:border-red-700 active:bg-red-700
                                          active:border-red-700 focus:outline-none focus:ring-4 focus:ring-red-300<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-yellow-600 text-white
                                          border-yellow-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-yellow-700 hover:border-yellow-700
                                          active:bg-yellow-700 active:border-yellow-700 focus:outline-none focus:ring-4
                                          focus:ring-yellow-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-gray-800 text-white
                                          border-gray-800 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-gray-900 hover:border-gray-900 active:bg-gray-900
                                          active:border-gray-900 focus:outline-none focus:ring-4
                                          focus:ring-gray-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-gray-300 text-white
                                          border-gray-300 disabled:opacity-50 disabled:pointer-events-none
                                          hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700
                                          active:border-gray-700 focus:outline-none focus:ring-4
                                          focus:ring-gray-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-white text-gray-800
                                          border-white disabled:opacity-50 disabled:pointer-events-none hover:text-white
                                          hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700
                                          active:border-gray-700 focus:outline-none focus:ring-4
                                          focus:ring-gray-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Sizes</h2>
                        <p>Buttons stacked small to large sizes.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-sizebutton-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-sizebutton" type="button" role="tab"
                                 aria-controls="pills-design-sizebutton" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-sizebutton-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-sizebutton" type="button" role="tab"
                                 aria-controls="pills-html-sizebutton" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-sizebutton" role="tabpanel"
                              aria-labelledby="pills-design-sizebutton-tab" tabindex="0">
                              <button type="button"
                                 class="btn btn-sm gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                 Small Button
                              </button>
                              <button type="button"
                                 class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                 Button
                              </button>
                              <button type="button"
                                 class="btn btn-lg gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                 Large Button
                              </button>
                           </div>
                           <div class="tab-pane fade" id="pills-html-sizebutton" role="tabpanel"
                              aria-labelledby="pills-html-sizebutton-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-sm gap-x-2 bg-indigo-600
                                          text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Small Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn btn-lg gap-x-2 bg-indigo-600
                                          text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4
                                          focus:ring-indigo-300<span class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Large Button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end buttons page -->

   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>