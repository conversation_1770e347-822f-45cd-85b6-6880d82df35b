<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Progressbar - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Progressbar - Dash UI - TailwindCSS HTML Admin Template Free</title>
</head>

<body>
   <main>
      <!-- start progress page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "progress", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Progressbar</h1>
                  <p class="text-lg">A progress bar displays the status of a given process.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Example</h2>
                        <p>Determinate progress bars fill the container from 0 to 100%. This reflects the progress of
                           the process.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-progressbar-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-progressbar" type="button" role="tab"
                                 aria-controls="pills-design-progressbar" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-progressbar-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-progressbar" type="button" role="tab"
                                 aria-controls="pills-html-progressbar" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-progressbar" role="tabpanel"
                              aria-labelledby="pills-design-progressbar-tab" tabindex="0">
                              <div class="w-full bg-gray-200 rounded-full h-1.5">
                                 <div class="bg-indigo-600 h-1.5 rounded-full" style="width: 15%"></div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-progressbar" role="tabpanel"
                              aria-labelledby="pills-html-progressbar-tab" tabindex="0">
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-600 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Color variants</h2>
                        <p>Change the appearance of individual progress bars.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-progressbar-color-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-progressbar-color" type="button" role="tab"
                                 aria-controls="pills-design-progressbar-color" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-progressbar-color-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-progressbar-color" type="button" role="tab"
                                 aria-controls="pills-html-progressbar-color" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-progressbar-color" role="tabpanel"
                              aria-labelledby="pills-design-progressbar-color-tab" tabindex="0">
                              <div class="space-y-3">
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-indigo-600 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-gray-300 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-teal-500 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-red-500 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                                 <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-gray-800 h-1.5 rounded-full" style="width: 15%"></div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-progressbar-color" role="tabpanel"
                              aria-labelledby="pills-html-progressbar-color-tab" tabindex="0">
                              <pre class="language-markup" tabindex="0"><code class="language-markup">
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-600 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-300 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-teal-500 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-blue-500 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-red-500 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-yellow-500 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>w-full bg-gray-200 rounded-full
                                          h-1.5<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-800 h-1.5 rounded-full<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">style</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>width: 15%<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                 </code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end progress page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>