<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Collapse - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Collapse - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start collapse page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "collapse", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Collapse</h1>
                  <p class="text-lg">Toggle the visibility of content.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Collapse</h2>
                        <p>Click the buttons below to show and hide another element via class changes.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-collapse-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-collapse" type="button" role="tab"
                                 aria-controls="pills-design-collapse" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-collapse-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-collapse" type="button" role="tab"
                                 aria-controls="pills-html-collapse" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-collapse" role="tabpanel"
                              aria-labelledby="pills-design-collapse-tab" tabindex="0">
                              <a class="btn gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300 collapsed"
                                 href="#!" data-bs-toggle="collapse" data-bs-target="#collapseContent"
                                 aria-expanded="false" aria-controls="collapseContent">
                                 Collapse
                              </a>
                              <div id="collapseContent" class="collapse">
                                 <div class="card card-body shadow border mt-4">
                                    Some placeholder content for the collapse component. This panel is hidden by default
                                    but revealed when the user activates the relevant trigger.
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-collapse" role="tabpanel"
                              aria-labelledby="pills-html-collapse-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn gap-x-2 bg-indigo-600 text-white
                                          border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300
                                          collapsed<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#!<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>collapse<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-target</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#collapseContent<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-expanded</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>false<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>collapseContent<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Collapse
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>collapseContent<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>collapse<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>card card-bodyshadow border mt-4<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Some placeholder content for the collapse component. This panel is hidden by default
                                    but revealed when the user activates the relevant trigger.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end collapse page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>