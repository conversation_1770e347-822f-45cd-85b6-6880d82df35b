<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html")@@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Breadcrumb - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Breadcrumb - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start breadcrumb page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "breadcrumb", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Breadcrumb</h1>
                  <p class="text-lg">Breadcrumbs are a navigation system used to show a user's location in a site or
                     app.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Example</h2>
                        <p>Use an ordered or unordered list with linked list items to create a minimally styled
                           breadcrumb. Use our utilities to add additional styles as desired.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-breadcrumb-example-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-breadcrumb-example" type="button" role="tab"
                                 aria-controls="pills-design-breadcrumb-example" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-breadcrumb-example-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-breadcrumb-example" type="button" role="tab"
                                 aria-controls="pills-html-breadcrumb-example" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-breadcrumb-example" role="tabpanel"
                              aria-labelledby="pills-design-breadcrumb-example-tab" tabindex="0">
                              <ol class="flex items-center whitespace-nowrap" aria-label="Breadcrumb">
                                 <li class="inline-flex items-center">
                                    <a class="flex items-center text-base text-gray-500 hover:text-indigo-600 focus:outline-none focus:text-indigo-600"
                                       href="#">Home</a>
                                    <svg class="flex-shrink-0 h-5 w-5 text-gray-400 mx-2" width="16" height="16"
                                       viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                                       aria-hidden="true">
                                       <path d="M6 13L10 3" stroke="currentColor" stroke-linecap="round" />
                                    </svg>
                                 </li>
                                 <li class="inline-flex items-center">
                                    <a class="flex items-center text-base text-gray-500 hover:text-indigo-600 focus:outline-none focus:text-indigo-600"
                                       href="#">
                                       Library
                                       <svg class="flex-shrink-0 h-5 w-5 text-gray-400 mx-2" width="16" height="16"
                                          viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                                          aria-hidden="true">
                                          <path d="M6 13L10 3" stroke="currentColor" stroke-linecap="round" />
                                       </svg>
                                    </a>
                                 </li>
                                 <li class="inline-flex items-center text-base font-semibold text-gray-800 truncate"
                                    aria-current="page">Data</li>
                              </ol>
                           </div>
                           <div class="tab-pane fade" id="pills-html-breadcrumb-example" role="tabpanel"
                              aria-labelledby="pills-html-breadcrumb-example-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ol</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center whitespace-nowrap<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-label</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Breadcrumb<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center text-base text-gray-500
                                          hover:text-indigo-600 focus:outline-none focus:text-indigo-600 <span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Home<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex-shrink-0 h-5 w-5 text-gray-400
                                          mx-2<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">width</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">height</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 16 16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>M6 13L10 3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center text-base text-gray-500
                                          hover:text-indigo-600 focus:outline-none focus:text-indigo-600<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Library
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex-shrink-0 h-5 w-5 text-gray-400
                                          mx-2<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">width</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">height</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 16 16<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-hidden</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>M6 13L10 3<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center text-base
                                          font-semibold text-gray-800 truncate<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-current</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>page<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Data<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ol</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Divider</h2>
                        <p>The simple form of chevron breadcrumbs.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-breadcrumb-arrow-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-breadcrumb-arrow" type="button" role="tab"
                                 aria-controls="pills-design-breadcrumb-arrow" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-breadcrumb-arrow-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-breadcrumb-arrow" type="button" role="tab"
                                 aria-controls="pills-html-breadcrumb-arrow" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-breadcrumb-arrow" role="tabpanel"
                              aria-labelledby="pills-design-breadcrumb-arrow-tab" tabindex="0">
                              <ol class="flex items-center whitespace-nowrap" aria-label="Breadcrumb">
                                 <li class="inline-flex items-center">
                                    <a class="flex items-center text-base text-gray-500 hover:text-indigo-600 focus:outline-none focus:text-indigo-600"
                                       href="#">Home</a>
                                    <svg class="flex-shrink-0 mx-2 overflow-visible h-4 w-4 text-gray-400"
                                       xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                       fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                       stroke-linejoin="round">
                                       <path d="m9 18 6-6-6-6" />
                                    </svg>
                                 </li>
                                 <li class="inline-flex items-center">
                                    <a class="flex items-center text-base text-gray-500 hover:text-indigo-600 focus:outline-none focus:text-indigo-600"
                                       href="#">
                                       Library
                                       <svg class="flex-shrink-0 mx-2 overflow-visible h-4 w-4 text-gray-400"
                                          xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                          fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round">
                                          <path d="m9 18 6-6-6-6" />
                                       </svg>
                                    </a>
                                 </li>
                                 <li class="inline-flex items-center text-base font-semibold text-gray-800 truncate"
                                    aria-current="page">Data</li>
                              </ol>
                           </div>
                           <!-- code -->
                           <div class="tab-pane fade" id="pills-html-breadcrumb-arrow" role="tabpanel"
                              aria-labelledby="pills-html-breadcrumb-arrow-tab" tabindex="0">
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ol</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center whitespace-nowrap<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-label</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>Breadcrumb<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center text-base text-gray-500
                                          hover:text-indigo-600 focus:outline-none focus:text-indigo-600<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Home<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex-shrink-0 mx-2 overflow-visible h-4
                                          w-4 text-gray-400<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">width</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">height</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 24 24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m9 18 6-6-6-6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex items-center text-base text-gray-500
                                          hover:text-indigo-600 focus:outline-none focus:text-indigo-600<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Library
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>svg</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex-shrink-0 mx-2 overflow-visible h-4
                                          w-4 text-gray-400<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">xmlns</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">width</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">height</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">viewBox</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0 0 24 24<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">fill</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>none<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>currentColor<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-width</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-linecap</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">stroke-linejoin</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>round<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>path</span> <span
                                          class="token attr-name">d</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>m9 18 6-6-6-6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token punctuation">/&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>svg</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center text-base
                                          font-semibold text-gray-800 truncate<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-current</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>page<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Data<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ol</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end breadcrumb page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>