<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Navs-Tabs - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Navs-Tabs - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start navs tabs page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "navs-tabs", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Navs & Tabs</h1>
                  <p class="text-lg">Navigation components using Tailwind CSS.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Example</h2>
                        <p>The base nav component is built with flexbox and provide a strong foundation for building all
                           types of navigation components.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-nav-example-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-nav-example" type="button" role="tab"
                                 aria-controls="pills-design-nav-example" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-nav-example-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-nav-example" type="button" role="tab"
                                 aria-controls="pills-html-nav-example" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-nav-example" role="tabpanel"
                              aria-labelledby="pills-design-nav-example-tab" tabindex="0">
                              <nav class="flex space-x-6">
                                 <a class="inline-flex items-center gap-x-2 text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70 focus:outline-none focus:text-indigo-700"
                                    href="#">
                                    Link
                                 </a>
                                 <a class="inline-flex items-center gap-x-2 text-base font-semibold whitespace-nowrap text-indigo-600 hover:text-indigo-70 focus:outline-none focus:text-indigo-700"
                                    href="#" aria-current="page">
                                    Active
                                 </a>
                                 <a class="inline-flex items-center gap-x-2 text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70 focus:outline-none focus:text-indigo-700"
                                    href="#">
                                    Link
                                 </a>
                                 <a class="inline-flex items-center gap-x-2 text-base whitespace-nowrap text-gray-600 hover:text-indigo-70 focus:outline-none focus:text-indigo-700 opacity-50 pointer-events-none"
                                    href="#">
                                    Disabled
                                 </a>
                              </nav>
                           </div>
                           <div class="tab-pane fade" id="pills-html-nav-example" role="tabpanel"
                              aria-labelledby="pills-html-nav-example-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>nav</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex space-x-6<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center gap-x-2
                                          text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Link<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center gap-x-2
                                          text-base font-semibold whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-current</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>page<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Active
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center gap-x-2
                                          text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Link<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>inline-flex items-center gap-x-2
                                          text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700 opacity-50 pointer-events-none<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Disabled
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>nav</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Tabs</h2>
                        <p>The base nav component is built with flexbox and provide a strong foundation for building all
                           types of navigation components.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active" id="pills-design-tabs-tab"
                                 data-bs-toggle="pill" data-bs-target="#pills-design-tabs" type="button" role="tab"
                                 aria-controls="pills-design-tabs" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-tabs-tab" data-bs-toggle="pill" data-bs-target="#pills-html-tabs"
                                 type="button" role="tab" aria-controls="pills-html-tabs" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active" id="pills-design-tabs" role="tabpanel"
                              aria-labelledby="pills-design-tabs-tab" tabindex="0">
                              <nav class="flex border-b border-gray-300">
                                 <a class="px-4 py-3 rounded-t-md border border-indigo-600 hover:border-gray-300 active"
                                    href="#" aria-current="page">Active</a>
                                 <a class="px-4 py-3 rounded-t-md border border-white hover:border-gray-300"
                                    href="#">Link</a>

                                 <a class="px-4 py-3 rounded-t-md border border-white hover:border-gray-300"
                                    href="#">Link</a>
                                 <a class="px-4 py-3 rounded-t-md border border-white hover:border-gray-300 pointer-events-none"
                                    href="#">Disabled</a>
                              </nav>
                           </div>
                           <div class="tab-pane fade" id="pills-html-tabs" role="tabpanel"
                              aria-labelledby="pills-html-tabs-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>nav</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>flex border-b border-gray-300<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-4 py-3 -mb-px border-gray-300 border
                                          rounded-t-md inline-flex items-center gap-x-2 text-base font-semibold
                                          whitespace-nowrap text-indigo-600 hover:text-indigo-70 focus:outline-none
                                          focus:text-indigo-700<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-current</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>page<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Active
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-4 py-3 hover:-mb-px
                                          hover:border-gray-300 hover:border hover:rounded-t-md inline-flex items-center
                                          gap-x-2 text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Link
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-4 py-3 hover:-mb-px
                                          hover:border-gray-300 hover:border hover:rounded-t-md inline-flex items-center
                                          gap-x-2 text-base whitespace-nowrap text-indigo-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Link
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-4 py-3 hover:-mb-px
                                          hover:border-gray-300 hover:border hover:rounded-t-md inline-flex items-center
                                          gap-x-2 text-base whitespace-nowrap text-gray-600 hover:text-indigo-70
                                          focus:outline-none focus:text-indigo-700 opacity-50 pointer-events-none<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Disabled
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>nav</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Javascript Behaviour Tabs</h2>
                        <p>The base nav component is built with flexbox and provide a strong foundation for building all
                           types of navigation components.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabThird" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-tabs-javascript-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-tabs-javascript" type="button" role="tab"
                                 aria-controls="pills-design-tabs-javascript" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-tabs-javascript-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-tabs-javascript" type="button" role="tab"
                                 aria-controls="pills-html-tabs-javascript" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabThirdContent">
                           <div class="tab-pane fade show active" id="pills-design-tabs-javascript" role="tabpanel"
                              aria-labelledby="pills-design-tabs-javascript" tabindex="0">
                              <!-- nav -->
                              <ul class="nav nav-tabs mb-3 border-b border-gray-300" id="pills-tab2" role="tablist">
                                 <li class="nav-item" role="presentation">
                                    <button
                                       class="nav-link px-5 py-3 rounded-t-md border border-white hover:border-gray-300 active"
                                       id="pills-active-example1-tab" data-bs-toggle="pill"
                                       data-bs-target="#pills-active-example1" type="button" role="tab"
                                       aria-controls="pills-active-example1" aria-selected="true">
                                       Active
                                    </button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                    <button
                                       class="nav-link px-5 py-3 rounded-t-md border border-white hover:border-gray-300"
                                       id="pills-link1-example1-tab" data-bs-toggle="pill"
                                       data-bs-target="#pills-link1-example1" type="button" role="tab"
                                       aria-controls="pills-link1-example1" aria-selected="false">
                                       Link
                                    </button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                    <button
                                       class="nav-link px-5 py-3 rounded-t-md border border-white hover:border-gray-300"
                                       id="pills-link2-example1-tab" data-bs-toggle="pill"
                                       data-bs-target="#pills-link2-example1" type="button" role="tab"
                                       aria-controls="pills-link2-example1" aria-selected="false">
                                       Link
                                    </button>
                                 </li>
                              </ul>
                              <!-- nav content -->
                              <div class="tab-content py-6" id="pills-tab2Content">
                                 <div class="tab-pane fade show active" id="pills-active-example1" role="tabpanel"
                                    aria-labelledby="pills-active-example1" tabindex="0">
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. In elit ligula, cursus at
                                    orci at, condimentum dignissim dolor. Morbi elit lacus, posuere id urna et,
                                    elementum malesuada erat.
                                 </div>
                                 <div class="tab-pane fade" id="pills-link1-example1" role="tabpanel"
                                    aria-labelledby="pills-link1-example1" tabindex="0">
                                    Duis metus nisl, varius non mauris sed, commodo sodales arcu. Quisque in tellus
                                    semper, tincidunt tortor ut, varius purus. Aliquam erat volutpat.
                                 </div>
                                 <div class="tab-pane fade" id="pills-link2-example1" role="tabpanel"
                                    aria-labelledby="pills-link2-example1" tabindex="0">
                                    Mauris sed interdum elit, in sagittis erat. Aliquam sed venenatis nibh. Sed
                                    malesuada ornare leo, et consectetur arcu placerat eget. Praesent quam massa,
                                    vulputate sed ligula quis, aliquet rutrum enim.
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-tabs-javascript" role="tabpanel"
                              aria-labelledby="pills-html-tabs-javascript" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav nav-tabs mb-3 border-b
                                          border-gray-300<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-tab2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tablist<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>presentation<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-link px-5 py-3 rounded-t-md border
                                          border-white hover:border-gray-300 active<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-active-example1-tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pill<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#pills-active-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-active-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-selected</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>true<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Active
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>presentation<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-link px-5 py-3 rounded-t-md border
                                          border-white hover:border-gray-300<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link1-example1-tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pill<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#pills-link1-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link1-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-selected</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>false<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Link
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>presentation<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>a</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>nav-link px-5 py-3 rounded-t-md border
                                          border-white hover:border-gray-300<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link2-example1-tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pill<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#pills-link2-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-controls</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link2-example1<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-selected</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>false<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Link
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab-content py-6<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-tabContent<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab-pane fade show active<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-active-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tabpanel<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-active-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. In elit ligula, cursus at
                                    orci at, condimentum dignissim dolor. Morbi elit lacus, posuere id urna et,
                                    elementum
                                    malesuada erat.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab-pane fade<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link1-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tabpanel<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link1-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Duis metus nisl, varius non mauris sed, commodo sodales arcu. Quisque in tellus
                                    semper, tincidunt tortor ut, varius purus. Aliquam erat volutpat.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tab-pane fade<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">id</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link2-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>tabpanel<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">aria-labelledby</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>pills-link2-example1<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">tabindex</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>0<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    Mauris sed interdum elit, in sagittis erat. Aliquam sed venenatis nibh. Sed
                                    malesuada ornare leo, et consectetur arcu placerat eget. Praesent quam massa,
                                    vulputate sed
                                    ligula quis, aliquet rutrum enim.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end navs tabs page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>