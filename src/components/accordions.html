<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Accordions - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Accordions - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start accordions page -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "accordion", "page_group": "components" })
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Accordion</h1>
                  <p class="text-lg">Build vertically collapsing accordions in combination with our Collapse JavaScript
                     plugin.</p>
               </div>
               <div class="flex flex-col">
                  <div id="accordion-example" class="mb-4">
                     <h2 class="text-lg">Example</h2>
                     <p>Click the accordions below to expand/collapse the accordion content.</p>
                  </div>
                  <div class="card shadow">
                     <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                           <button class="p-3 px-6 border-b nav-link font-semibold active" id="pills-design-tab"
                              data-bs-toggle="pill" data-bs-target="#pills-design" type="button" role="tab"
                              aria-controls="pills-design" aria-selected="true">
                              Design
                           </button>
                        </li>
                        <li class="nav-item" role="presentation">
                           <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                              id="pills-html-tab" data-bs-toggle="pill" data-bs-target="#pills-html" type="button"
                              role="tab" aria-controls="pills-html" aria-selected="false">
                              HTML
                           </button>
                        </li>
                     </ul>
                     <div class="tab-content p-6" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-design" role="tabpanel"
                           aria-labelledby="pills-design-tab" tabindex="0">
                           <div class="accordion" id="accordionExample">
                              <div class="border border-gray-400 rounded-md mb-3">
                                 <h2 class="accordion-header px-4 py-3">
                                    <button class="text-lg fw-semibold flex items-center w-full justify-between"
                                       type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                       aria-expanded="true" aria-controls="collapseOne">
                                       <span>Accordion Item #1</span>
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-chevron-down collapse-icon">
                                             <polyline points="6 9 12 15 18 9"></polyline>
                                          </svg>
                                       </span>
                                    </button>
                                 </h2>
                                 <div id="collapseOne" class="accordion-collapse collapse show"
                                    data-bs-parent="#accordionExample">
                                    <div class="accordion-body px-4 py-3 border-t border-gray-400">
                                       <strong>This is the first item's accordion body.</strong>
                                       It is shown by default, until the collapse plugin adds the appropriate classes
                                       that we use to style each element. These classes control the overall
                                       appearance, as well as the showing and hiding via CSS transitions. You can modify
                                       any of this with custom CSS or overriding our default variables. It's also
                                       worth noting that just about any HTML can go within the
                                       <code>.accordion-body</code>
                                       , though the transition does limit overflow.
                                    </div>
                                 </div>
                              </div>
                              <div class="border border-gray-400 rounded-md mb-3">
                                 <h2 class="accordion-header px-4 py-3">
                                    <button class="text-lg fw-semibold flex items-center w-full justify-between"
                                       type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                       aria-expanded="false" aria-controls="collapseTwo">
                                       <span>Accordion Item #2</span>
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-chevron-down collapse-icon">
                                             <polyline points="6 9 12 15 18 9"></polyline>
                                          </svg>
                                       </span>
                                    </button>
                                 </h2>
                                 <div id="collapseTwo" class="accordion-collapse collapse"
                                    data-bs-parent="#accordionExample">
                                    <div class="accordion-body px-4 py-3 border-t border-gray-400">
                                       <strong>This is the second item's accordion body.</strong>
                                       It is hidden by default, until the collapse plugin adds the appropriate classes
                                       that we use to style each element. These classes control the overall
                                       appearance, as well as the showing and hiding via CSS transitions. You can modify
                                       any of this with custom CSS or overriding our default variables. It's also
                                       worth noting that just about any HTML can go within the
                                       <code>.accordion-body</code>
                                       , though the transition does limit overflow.
                                    </div>
                                 </div>
                              </div>
                              <div class="border border-gray-400 rounded-md mb-3">
                                 <h2 class="accordion-header px-4 py-3">
                                    <button class="text-lg fw-semibold flex items-center w-full justify-between"
                                       type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree"
                                       aria-expanded="false" aria-controls="collapseThree">
                                       <span>Accordion Item #3</span>
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-chevron-down collapse-icon">
                                             <polyline points="6 9 12 15 18 9"></polyline>
                                          </svg>
                                       </span>
                                    </button>
                                 </h2>
                                 <div id="collapseThree" class="accordion-collapse collapse"
                                    data-bs-parent="#accordionExample">
                                    <div class="accordion-body px-4 py-3 border-t border-gray-400">
                                       <strong>This is the third item's accordion body.</strong>
                                       It is hidden by default, until the collapse plugin adds the appropriate classes
                                       that we use to style each element. These classes control the overall
                                       appearance, as well as the showing and hiding via CSS transitions. You can modify
                                       any of this with custom CSS or overriding our default variables. It's also
                                       worth noting that just about any HTML can go within the
                                       <code>.accordion-body</code>
                                       , though the transition does limit overflow.
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="tab-pane fade" id="pills-html" role="tabpanel" aria-labelledby="pills-html-tab"
                           tabindex="0">
                           <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">id</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordionExample<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>border border-gray-400 rounded-md mb-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>h2</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-header px-4 py-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>button</span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>text-lg fw-semibold flex items-center w-full
                                       justify-between<span class="token punctuation">"</span></span>
                                    <span class="token attr-name">type</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>button<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-toggle</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapse<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-target</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#collapseOne<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-expanded</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>true<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-controls</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseOne<span
                                          class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>Accordion Item #1<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>svg</span>
                                    <span class="token attr-name">xmlns</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">width</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">height</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">viewBox</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>0 0 24 24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">fill</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>none<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>currentColor<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-width</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>2<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linecap</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linejoin</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>feather feather-chevron-down
                                       collapse-icon<span class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>polyline</span> <span
                                       class="token attr-name">points</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>6 9 12 15 18 9<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span><span class="token tag"><span
                                       class="token tag"><span
                                          class="token punctuation">&lt;/</span>polyline</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>svg</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>button</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>h2</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">id</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseOne<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-collapse collapse show<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">data-bs-parent</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#accordionExample<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-body px-4 py-3 border-t
                                       border-gray-400<span class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>This is the first item's accordion
                                 body.<span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>
                                 It is shown by default, until the collapse plugin adds the appropriate classes that we
                                 use to style each element. These classes control the overall appearance, as
                                 well as the showing and hiding via CSS transitions. You can modify any of this with
                                 custom CSS or overriding our default variables. It's also worth noting that just
                                 about any HTML can go within the
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>code</span><span
                                       class="token punctuation">&gt;</span></span>.accordion-body<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>code</span><span
                                       class="token punctuation">&gt;</span></span>
                                 , though the transition does limit overflow.
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>border border-gray-400 rounded-md mb-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>h2</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-header px-4 py-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>button</span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>text-lg fw-semibold flex items-center w-full
                                       justify-between<span class="token punctuation">"</span></span>
                                    <span class="token attr-name">type</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>button<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-toggle</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapse<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-target</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#collapseTwo<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-expanded</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>false<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-controls</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseTwo<span
                                          class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>Accordion Item #2<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>svg</span>
                                    <span class="token attr-name">xmlns</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">width</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">height</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">viewBox</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>0 0 24 24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">fill</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>none<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>currentColor<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-width</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>2<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linecap</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linejoin</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>feather feather-chevron-down
                                       collapse-icon<span class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>polyline</span> <span
                                       class="token attr-name">points</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>6 9 12 15 18 9<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span><span class="token tag"><span
                                       class="token tag"><span
                                          class="token punctuation">&lt;/</span>polyline</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>svg</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>button</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>h2</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">id</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseTwo<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-collapse collapse<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">data-bs-parent</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#accordionExample<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-body px-4 py-3 border-t
                                       border-gray-400<span class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>This is the second item's accordion
                                 body.<span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>
                                 It is hidden by default, until the collapse plugin adds the appropriate classes that we
                                 use to style each element. These classes control the overall appearance, as
                                 well as the showing and hiding via CSS transitions. You can modify any of this with
                                 custom CSS or overriding our default variables. It's also worth noting that just
                                 about any HTML can go within the
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>code</span><span
                                       class="token punctuation">&gt;</span></span>.accordion-body<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>code</span><span
                                       class="token punctuation">&gt;</span></span>
                                 , though the transition does limit overflow.
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>border border-gray-400 rounded-md mb-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>h2</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-header px-4 py-3<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>button</span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>text-lg fw-semibold flex items-center w-full
                                       justify-between<span class="token punctuation">"</span></span>
                                    <span class="token attr-name">type</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>button<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-toggle</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapse<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">data-bs-target</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#collapseThree<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-expanded</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>false<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">aria-controls</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseThree<span
                                          class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>Accordion Item #3<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>svg</span>
                                    <span class="token attr-name">xmlns</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>http://www.w3.org/2000/svg<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">width</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">height</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">viewBox</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>0 0 24 24<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">fill</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>none<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>currentColor<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-width</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>2<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linecap</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">stroke-linejoin</span><span
                                       class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>round<span
                                          class="token punctuation">"</span></span>
                                    <span class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>feather feather-chevron-down
                                       collapse-icon<span class="token punctuation">"</span></span>
                                    <span class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>polyline</span> <span
                                       class="token attr-name">points</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>6 9 12 15 18 9<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span><span class="token tag"><span
                                       class="token tag"><span
                                          class="token punctuation">&lt;/</span>polyline</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>svg</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>span</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>button</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>h2</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">id</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>collapseThree<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-collapse collapse<span
                                          class="token punctuation">"</span></span> <span
                                       class="token attr-name">data-bs-parent</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>#accordionExample<span
                                          class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>div</span> <span
                                       class="token attr-name">class</span><span class="token attr-value"><span
                                          class="token punctuation attr-equals">=</span><span
                                          class="token punctuation">"</span>accordion-body px-4 py-3 border-t
                                       border-gray-400<span class="token punctuation">"</span></span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>This is the third item's accordion
                                 body.<span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>strong</span><span
                                       class="token punctuation">&gt;</span></span>
                                 It is hidden by default, until the collapse plugin adds the appropriate classes that we
                                 use to style each element. These classes control the overall appearance, as
                                 well as the showing and hiding via CSS transitions. You can modify any of this with
                                 custom CSS or overriding our default variables. It's also worth noting that just
                                 about any HTML can go within the
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;</span>code</span><span
                                       class="token punctuation">&gt;</span></span>.accordion-body<span
                                    class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>code</span><span
                                       class="token punctuation">&gt;</span></span>
                                 , though the transition does limit overflow.
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span>
                                 <span class="token tag"><span class="token tag"><span
                                          class="token punctuation">&lt;/</span>div</span><span
                                       class="token punctuation">&gt;</span></span></code></pre>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>