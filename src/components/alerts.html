<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Alerts - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Alerts - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start alert page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "alerts", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Alerts</h1>
                  <p class="text-lg">Provide contextual feedback messages for typical user actions with the handful of
                     available and flexible alert messages.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Solid color variants</h2>
                        <p>Use the following examples of alert components to show messages as feedback to your users.
                        </p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active" id="pills-design-tab"
                                 data-bs-toggle="pill" data-bs-target="#pills-design" type="button" role="tab"
                                 aria-controls="pills-design" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-tab" data-bs-toggle="pill" data-bs-target="#pills-html" type="button"
                                 role="tab" aria-controls="pills-html" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design" role="tabpanel"
                              aria-labelledby="pills-design-tab" tabindex="0">
                              <div class="bg-indigo-600 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Info</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-gray-500 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Secondary</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-teal-500 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Success</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-blue-600 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Info</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-red-500 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Danger</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-yellow-500 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Warning</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-gray-800 mb-3 text-white rounded-lg p-4" role="alert">
                                 <span class="font-bold">Dark</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                              <div class="bg-gray-300 mb-3 text-gray-600 rounded-lg p-4" role="alert">
                                 <span class="font-bold">Light</span>
                                 alert! You should check in on some of those fields below.
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html" role="tabpanel" aria-labelledby="pills-html-tab"
                              tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-indigo-600 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Info<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-500 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Secondary<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-teal-500 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Success<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-blue-600 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Info<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-red-500 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Danger<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-yellow-500 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Warning<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-800 mb-3 text-white rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Dark<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-300 mb-3 text-gray-600 rounded-lg
                                          p-4<span class="token punctuation">"</span></span> <span
                                          class="token attr-name">role</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>alert<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>span</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>font-bold<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Light<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>span</span><span
                                          class="token punctuation">&gt;</span></span>
                                    alert! You should check in on some of those fields below.
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                           <!-- code -->
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
      <!-- end alert page -->
   </main>

   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>