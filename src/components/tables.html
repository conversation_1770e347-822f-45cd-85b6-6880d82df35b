<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Tables - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Tables - Dash UI - TailwindCSS HTML Admin Template Free</title>
</head>

<body>
   <main>
      <!-- start tables page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "tables", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")

            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Tables</h1>
                  <p class="text-lg">Using the most basic table markup, here's how tables look.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <!-- table start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Basic Example</h2>
                        <p>Using the most basic table markup, here's how tables look.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-basic-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-basic-table" type="button" role="tab"
                                 aria-controls="pills-design-basic-table" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-basic-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-basic-table" type="button" role="tab"
                                 aria-controls="pills-html-basic-table" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active rounded-md" id="pills-design-basic-table"
                              role="tabpanel" aria-labelledby="pills-design-basic-table-tab" tabindex="0">
                              <div class="relative overflow-x-auto">
                                 <!-- table -->
                                 <table class="text-left w-full whitespace-nowrap">
                                    <thead class="bg-gray-200 text-gray-700">
                                       <tr class="border-gray-300 border-b">
                                          <th scope="col" class="px-6 py-3">#</th>
                                          <th scope="col" class="px-6 py-3">First</th>
                                          <th scope="col" class="px-6 py-3">Last</th>
                                          <th scope="col" class="px-6 py-3">Handle</th>
                                       </tr>
                                    </thead>
                                    <tbody class="divide-y">
                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left">1</td>
                                          <td class="py-3 px-6 text-left">Mark</td>
                                          <td class="py-3 px-6 text-left">Otto</td>
                                          <td class="py-3 px-6 text-left">@mdo</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left">2</td>
                                          <td class="py-3 px-6 text-left">Jacob</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                          <td class="py-3 px-6 text-left">@fat</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left">3</td>
                                          <td colspan="2" class="py-3 px-6 text-left">Larry the Bird</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-basic-table" role="tabpanel"
                              aria-labelledby="pills-html-basic-table-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>relative overflow-x-auto<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>table</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-left w-full whitespace-nowrap<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>thead</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>bg-gray-200 text-gray-700 <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>#<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>First<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Last<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Handle<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>thead</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tbody</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>divide-y <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>1<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Mark<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Otto<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@mdo<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>2<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>3<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">colspan</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Larry the Bird<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tbody</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>table</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- table end -->

                  <!-- table striped start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Striped Table</h2>
                        <p>Use this example to increase the readability of the data sets by alternating the background
                           color of every second table row.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabSecond" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-striped-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-striped-table" type="button" role="tab"
                                 aria-controls="pills-design-striped-table" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-striped-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-striped-table" type="button" role="tab"
                                 aria-controls="pills-html-striped-table" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabSecondContent">
                           <div class="tab-pane fade show active rounded-md" id="pills-design-striped-table"
                              role="tabpanel" aria-labelledby="pills-design-striped-table-tab" tabindex="0">
                              <div class="relative overflow-x-auto">
                                 <!-- table -->
                                 <table class="text-left w-full whitespace-nowrap">
                                    <thead>
                                       <tr class="border-gray-300 border-b">
                                          <th scope="col" class="px-6 py-3">#</th>
                                          <th scope="col" class="px-6 py-3">First</th>
                                          <th scope="col" class="px-6 py-3">Last</th>
                                          <th scope="col" class="px-6 py-3">Handle</th>
                                       </tr>
                                    </thead>
                                    <tbody class="divide-y">
                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left">1</td>
                                          <td class="py-3 px-6 text-left">Mark</td>
                                          <td class="py-3 px-6 text-left">Otto</td>
                                          <td class="py-3 px-6 text-left">@mdo</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b bg-gray-100">
                                          <td class="py-3 px-6 text-left">2</td>
                                          <td class="py-3 px-6 text-left">Jacob</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                          <td class="py-3 px-6 text-left">@fat</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left">3</td>
                                          <td colspan="2" class="py-3 px-6 text-left">Larry the Bird</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                       </tr>
                                       <tr class="border-gray-300 border-b bg-gray-100">
                                          <td class="py-3 px-6 text-left">4</td>
                                          <td class="py-3 px-6 text-left">Jacob</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                          <td class="py-3 px-6 text-left">@fat</td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-striped-table" role="tabpanel"
                              aria-labelledby="pills-html-striped-table-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>relative overflow-x-auto<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>table</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-left w-full whitespace-nowrap<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>thead</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span><span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>#<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>First<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Last<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Handle<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>thead</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tbody</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>divide-y <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>1<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Mark<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Otto<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@mdo<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b bg-gray-100
                                          <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>2<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>3<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">colspan</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Larry the Bird<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b bg-gray-100
                                          <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>4<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tbody</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>table</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- table striped end -->
                  <!-- table hover start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Hoverable rows</h2>
                        <p>Add hover-state on table row.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabThird" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-hover-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-hover-table" type="button" role="tab"
                                 aria-controls="pills-design-hover-table" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-hover-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-hover-table" type="button" role="tab"
                                 aria-controls="pills-html-hover-table" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabThirdContent">
                           <div class="tab-pane fade show active rounded-md" id="pills-design-hover-table"
                              role="tabpanel" aria-labelledby="pills-design-hover-table-tab" tabindex="0">
                              <div class="relative overflow-x-auto">
                                 <table class="text-left w-full whitespace-nowrap">
                                    <thead>
                                       <tr class="border-gray-300 border-b">
                                          <th scope="col" class="px-6 py-3">#</th>
                                          <th scope="col" class="px-6 py-3">First</th>
                                          <th scope="col" class="px-6 py-3">Last</th>
                                          <th scope="col" class="px-6 py-3">Handle</th>
                                       </tr>
                                    </thead>
                                    <tbody class="divide-y">
                                       <tr class="border-gray-300 border-b hover:bg-gray-100">
                                          <td class="py-3 px-6 text-left">1</td>
                                          <td class="py-3 px-6 text-left">Mark</td>
                                          <td class="py-3 px-6 text-left">Otto</td>
                                          <td class="py-3 px-6 text-left">@mdo</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b hover:bg-gray-100">
                                          <td class="py-3 px-6 text-left">2</td>
                                          <td class="py-3 px-6 text-left">Jacob</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                          <td class="py-3 px-6 text-left">@fat</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b hover:bg-gray-100">
                                          <td class="py-3 px-6 text-left">3</td>
                                          <td colspan="2" class="py-3 px-6 text-left">Larry the Bird</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                       </tr>
                                       <tr class="border-gray-300 border-b hover:bg-gray-100">
                                          <td class="py-3 px-6 text-left">4</td>
                                          <td class="py-3 px-6 text-left">Jacob</td>
                                          <td class="py-3 px-6 text-left">Thornton</td>
                                          <td class="py-3 px-6 text-left">@fat</td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-hover-table" role="tabpanel"
                              aria-labelledby="pills-html-hover-table-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>relative overflow-x-auto<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>table</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-left w-full whitespace-nowrap<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>thead</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span><span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>#<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>First<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Last<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Handle<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>thead</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tbody</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>divide-y <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b
                                          hover:bg-gray-100 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>1<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Mark<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Otto<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@mdo<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b
                                          hover:bg-gray-100 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>2<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b
                                          hover:bg-gray-100 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>3<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">colspan</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Larry the Bird<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b
                                          hover:bg-gray-100 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>4<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tbody</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>table</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- table hover end -->
                  <!-- table bordered start -->
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Bordered tables</h2>
                        <p>Add border on all sides of the table and cells.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tabforuth" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-bordered-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-bordered-table" type="button" role="tab"
                                 aria-controls="pills-design-bordered-table" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->

                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-bordered-table-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-bordered-table" type="button" role="tab"
                                 aria-controls="pills-html-bordered-table" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabforuthContent">
                           <div class="tab-pane fade show active rounded-md" id="pills-design-bordered-table"
                              role="tabpanel" aria-labelledby="pills-design-bordered-table-tab" tabindex="0">
                              <div class="relative overflow-x-auto">
                                 <table class="text-left w-full whitespace-nowrap border border-gray-300">
                                    <thead>
                                       <tr class="border-gray-300 border-b">
                                          <th scope="col" class="px-6 py-3 border-r border-gray-300">#</th>
                                          <th scope="col" class="px-6 py-3 border-r border-gray-300">First</th>
                                          <th scope="col" class="px-6 py-3 border-r border-gray-300">Last</th>
                                          <th scope="col" class="px-6 py-3 border-r border-gray-300">Handle</th>
                                       </tr>
                                    </thead>
                                    <tbody class="divide-y">
                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left border-r border-gray-300">1</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Mark</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Otto</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">@mdo</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left border-r border-gray-300">2</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Jacob</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Thornton</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">@fat</td>
                                       </tr>

                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left border-r border-gray-300">3</td>
                                          <td colspan="2" class="py-3 px-6 text-left border-r border-gray-300">Larry the
                                             Bird</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Thornton</td>
                                       </tr>
                                       <tr class="border-gray-300 border-b">
                                          <td class="py-3 px-6 text-left border-r border-gray-300">4</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Jacob</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">Thornton</td>
                                          <td class="py-3 px-6 text-left border-r border-gray-300">@fat</td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-bordered-table" role="tabpanel"
                              aria-labelledby="pills-html-bordered-table-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"> <span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>table</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>text-left w-full whitespace-nowrap border
                                          border-gray-300<span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>thead</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span><span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3 border-r border-gray-300 <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>#<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3 border-r border-gray-300 <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>First<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3 border-r border-gray-300 <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Last<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>th</span> <span
                                          class="token attr-name">scope</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>col<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>px-6 py-3 border-r border-gray-300 <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Handle<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>th</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>thead</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tbody</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>divide-y <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>1<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Mark<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Otto<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@mdo<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>2<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>3<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">colspan</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>2<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Larry the Bird<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>tr</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>border-gray-300 border-b <span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>4<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Jacob<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Thornton<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>td</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>py-3 px-6 text-left border-r
                                          border-gray-300 <span class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>@fat<span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>td</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tr</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>tbody</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>table</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- table bordered end -->
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end tables page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>