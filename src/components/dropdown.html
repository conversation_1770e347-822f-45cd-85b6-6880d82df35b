<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/prismjs/themes/prism-okaidia.min.css" />
   @@include("../partials/head.html") @@include("../partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Dropdown - TailwindCSS HTML Admin Template Free - Dash UI" />
   <title>Dropdown - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start dropdown page -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("../partials/navbar-vertical.html", { "page": "dropdown", "page_group": "components" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("../partials/top-navbar.html")
            <div class="p-6">
               <div class="flex flex-col mb-4 border-b border-gray-300 pb-4">
                  <h1 class="block font-semibold leading-6 text-xl mb-1">Dropdown</h1>
                  <p class="text-lg">The Dropdown component is designed to present users with a neat and organized list
                     of actions or additional content.</p>
               </div>
               <div class="flex flex-col gap-8">
                  <div>
                     <div class="mb-4">
                        <h2 class="text-lg">Default Dropdown</h2>
                        <p>The default dropdown menu appearance.</p>
                     </div>
                     <!-- card -->
                     <div class="card shadow">
                        <!-- nav -->
                        <ul class="nav nav-line-bottom mb-3 border-b" id="pills-tab" role="tablist">
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 border-b nav-link font-semibold active"
                                 id="pills-design-default-dropdown-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-design-default-dropdown" type="button" role="tab"
                                 aria-controls="pills-design-default-dropdown" aria-selected="true">
                                 Design
                              </button>
                           </li>
                           <!-- nav item -->
                           <li class="nav-item" role="presentation">
                              <button class="p-3 px-6 font-semibold border-b border-transparent nav-link"
                                 id="pills-html-button-group-tab" data-bs-toggle="pill"
                                 data-bs-target="#pills-html-button-group" type="button" role="tab"
                                 aria-controls="pills-html-button-group" aria-selected="false">
                                 HTML
                              </button>
                           </li>
                        </ul>
                        <!-- tab content -->
                        <div class="tab-content p-6" id="pills-tabContent">
                           <div class="tab-pane fade show active" id="pills-design-default-dropdown" role="tabpanel"
                              aria-labelledby="pills-design-default-dropdown-tab" tabindex="0">
                              <!-- dropdown -->
                              <div class="dropdown">
                                 <button
                                    class="btn dropdown-toggle gap-x-2 bg-indigo-600 text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300 dropdown-toggle"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Dropdown button
                                 </button>
                                 <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">Action</a></li>
                                    <li><a class="dropdown-item" href="#">Another action</a></li>
                                    <li><a class="dropdown-item" href="#">Something else here</a></li>
                                 </ul>
                              </div>
                           </div>
                           <div class="tab-pane fade" id="pills-html-button-group" role="tabpanel"
                              aria-labelledby="pills-html-button-group-tab" tabindex="0">
                              <!-- code -->
                              <pre class="language-markup" tabindex="0"><code class="language-markup"><span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>div</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>button</span>
                                       <span class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>btn dropdown-toggle gap-x-2 bg-indigo-600
                                          text-white border-indigo-600 disabled:opacity-50 disabled:pointer-events-none
                                          hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800
                                          active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300
                                          dropdown-toggle<span class="token punctuation">"</span></span>
                                       <span class="token attr-name">type</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>button<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">data-bs-toggle</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown<span
                                             class="token punctuation">"</span></span>
                                       <span class="token attr-name">aria-expanded</span><span
                                          class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>false<span
                                             class="token punctuation">"</span></span>
                                       <span class="token punctuation">&gt;</span></span>
                                    Dropdown button
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>button</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>ul</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-menu<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Another action<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;</span>li</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;</span>a</span> <span
                                          class="token attr-name">class</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>dropdown-item<span
                                             class="token punctuation">"</span></span> <span
                                          class="token attr-name">href</span><span class="token attr-value"><span
                                             class="token punctuation attr-equals">=</span><span
                                             class="token punctuation">"</span>#<span
                                             class="token punctuation">"</span></span><span
                                          class="token punctuation">&gt;</span></span>Something else here<span
                                       class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>a</span><span
                                          class="token punctuation">&gt;</span></span><span class="token tag"><span
                                          class="token tag"><span class="token punctuation">&lt;/</span>li</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>ul</span><span
                                          class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span
                                             class="token punctuation">&lt;/</span>div</span><span
                                          class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            @@include("../partials/footer.html")
         </div>
      </div>
   </main>

   <!-- end dropdown page -->
   @@include("../partials/buy-template.html")
   @@include("../partials/scripts.html")
   <script src="@@webRoot/node_modules/prismjs/prism.js"></script>
   <script src="@@webRoot/node_modules/prismjs/components/prism-scss.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
   <script src="@@webRoot/node_modules/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
</body>

</html>