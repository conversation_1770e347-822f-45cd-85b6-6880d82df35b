<!-- start navbar -->
<nav class="navbar-vertical navbar">
   <div id="myScrollableElement" class="h-screen" data-simplebar>
      <!-- brand logo -->
      <a class="navbar-brand" href="@@webRoot/index.html">
         <img src="@@webRoot/assets/images/brand/logo/logo.svg" alt="" />
      </a>

      <!-- navbar nav -->
      <ul class="navbar-nav flex-col" id="sideNavbar">
         <li class="nav-item">
            <a class="nav-link @@if (context.page === 'dashboard') { active }" href="@@webRoot/index.html">
               <i data-feather="home" class="w-4 h-4 mr-2"></i>
               Dashboard
            </a>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <div class="navbar-heading">Layouts & Pages</div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a class="nav-link @@if (context.page_group !== 'pages') { collapsed }" href="#!" data-bs-toggle="collapse" data-bs-target="#navPages" aria-expanded="false" aria-controls="navPages">
               <i data-feather="layers" class="w-4 h-4 mr-2"></i>
               Pages
            </a>
            <div id="navPages" class="collapse @@if (context.page_group === 'pages') { show }" data-bs-parent="#sideNavbar">
               <ul class="nav flex-col">
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'profile') { active }" href="@@webRoot/profile.html">Profile</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'settings') { active }" href="@@webRoot/settings.html">Settings</a>
                  </li>

                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'billing') { active }" href="@@webRoot/billing.html">Billing</a>
                  </li>

                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'pricing') { active }" href="@@webRoot/pricing.html">Pricing</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === '404error') { active }" href="@@webRoot/404-error.html">404 Error</a>
                  </li>
               </ul>
            </div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a
               class="nav-link @@if (context.page_group !== 'authentication') { collapsed }"
               href="#!"
               data-bs-toggle="collapse"
               data-bs-target="#navAuthentication"
               aria-expanded="false"
               aria-controls="navAuthentication">
               <i data-feather="lock" class="w-4 h-4 mr-2"></i>
               Authentication
            </a>
            <div id="navAuthentication" class="collapse @@if (context.page_group === 'authentication') { show }" data-bs-parent="#sideNavbar">
               <ul class="nav flex-col">
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'signin') { active }" href="@@webRoot/sign-in.html">Sign In</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'signup') { active }" href="@@webRoot/sign-up.html">Sign Up</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'forgetpassword') { active }" href="@@webRoot/forget-password.html">Forget Password</a>
                  </li>
               </ul>
            </div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a class="nav-link @@if (context.page === 'layout') { active }" href="@@webRoot/layout.html">
               <i data-feather="sidebar" class="w-4 h-4 mr-2"></i>
               Layouts
            </a>
         </li>
         <!-- nav heading -->
         <li class="nav-item">
            <div class="navbar-heading">UI Components</div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a
               class="nav-link @@if (context.page_group !== 'components') { collapsed }"
               href="#!"
               data-bs-toggle="collapse"
               data-bs-target="#navComponents"
               aria-expanded="false"
               aria-controls="navComponents">
               <i data-feather="package" class="w-4 h-4 mr-2"></i>
               Components
            </a>
            <div id="navComponents" class="collapse @@if (context.page_group === 'components') { show }" data-bs-parent="#sideNavbar">
               <ul class="nav flex-col">
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'accordion') { active }" href="@@webRoot/components/accordions.html">Accordions</a>
                  </li>

                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'alerts') { active }" href="@@webRoot/components/alerts.html">Alerts</a>
                  </li>

                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'avatar') { active }" href="@@webRoot/components/avatar.html">Avatar</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'badge') { active }" href="@@webRoot/components/badge.html">Badges</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'buttons') { active }" href="@@webRoot/components/buttons.html">Buttons</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'button-group') { active }" href="@@webRoot/components/button-group.html">Button Group</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'blockquote') { active }" href="@@webRoot/components/blockquote.html">Blockquote</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'breadcrumb') { active }" href="@@webRoot/components/breadcrumb.html">Breadcrumb</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'card') { active }" href="@@webRoot/components/card.html">Card</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'collapse') { active }" href="@@webRoot/components/collapse.html">Collapse</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'dropdown') { active }" href="@@webRoot/components/dropdown.html">Dropdown</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'forms') { active }" href="@@webRoot/components/forms.html">Forms</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'offcanvas') { active }" href="@@webRoot/components/offcanvas.html">Offcanvas</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'lists') { active }" href="@@webRoot/components/lists.html">Lists</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'navbar') { active }" href="@@webRoot/components/navbar.html">Navbar</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'navs-tabs') { active }" href="@@webRoot/components/navs-tabs.html">Nav & Tabs</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'pagination') { active }" href="@@webRoot/components/pagination.html">Pagination</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'progress') { active }" href="@@webRoot/components/progress.html">Progress</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'spinners') { active }" href="@@webRoot/components/spinners.html">Spinners</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'tables') { active }" href="@@webRoot/components/tables.html">Tables</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'toast') { active }" href="@@webRoot/components/toast.html">Toast</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link @@if (context.page === 'tooltips') { active }" href="@@webRoot/components/tooltips.html">Tooltips</a>
                  </li>
               </ul>
            </div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a class="nav-link collapsed" href="#!" data-bs-toggle="collapse" data-bs-target="#navMenuLevel" aria-expanded="false" aria-controls="navMenuLevel">
               <i data-feather="corner-left-down" class="w-4 h-4 mr-2"></i>
               Menu Level
            </a>
            <div id="navMenuLevel" class="collapse" data-bs-parent="#sideNavbar">
               <ul class="nav flex-col">
                  <li class="nav-item">
                     <a class="nav-link" href="#!" data-bs-toggle="collapse" data-bs-target="#navMenuLevelSecond" aria-expanded="false" aria-controls="navMenuLevelSecond">Two Level</a>
                     <div id="navMenuLevelSecond" class="collapse" data-bs-parent="#navMenuLevel">
                        <ul class="nav flex-col">
                           <li class="nav-item">
                              <a class="nav-link" href="#!">NavItem 1</a>
                           </li>
                           <li class="nav-item">
                              <a class="nav-link" href="#!">NavItem 2</a>
                           </li>
                        </ul>
                     </div>
                  </li>
                  <!-- nav item -->
                  <li class="nav-item">
                     <a class="nav-link collapsed" href="#!" data-bs-toggle="collapse" data-bs-target="#navMenuLevelThree" aria-expanded="false" aria-controls="navMenuLevelThree">Three Level</a>
                     <div id="navMenuLevelThree" class="collapse" data-bs-parent="#navMenuLevel">
                        <ul class="nav flex-col">
                           <li class="nav-item">
                              <a class="nav-link collapsed" href="#!" data-bs-toggle="collapse" data-bs-target="#navMenuLevelThreeOne" aria-expanded="false" aria-controls="navMenuLevelThreeOne">
                                 NavItem 1
                              </a>
                              <div id="navMenuLevelThreeOne" class="collapse" data-bs-parent="#navMenuLevelThree">
                                 <ul class="nav flex-col">
                                    <li class="nav-item">
                                       <a class="nav-link" href="#!">NavChild Item 1</a>
                                    </li>
                                 </ul>
                              </div>
                           </li>
                           <li class="nav-item">
                              <a class="nav-link" href="#!">Nav Item 2</a>
                           </li>
                        </ul>
                     </div>
                  </li>
               </ul>
            </div>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <div class="navbar-heading">Documentation</div>
         </li>

         <!-- nav item -->
         <li class="nav-item">
            <a class="nav-link @@if (context.page === 'docs') { active }" href="@@webRoot/docs.html">
               <i data-feather="clipboard" class="w-4 h-4 mr-2"></i>
               Docs
            </a>
         </li>
         <!-- nav item -->
         <li class="nav-item">
            <a class="nav-link @@if (context.page === 'changelog') { active }" href="@@webRoot/changelog.html">
               <i data-feather="git-pull-request" class="w-4 h-4 mr-2"></i>
               Changelog
            </a>
         </li>
         <!-- nav heading -->
         <li class="nav-item">
            <a class="nav-link" href="https://dashui.codescandy.com/tailwindcss-admin-dashboard-html-template.html" target="_blank">
               <i data-feather="download" class="w-4 h-4 mr-2"></i>
               Download
            </a>
         </li>
      </ul>
   </div>
</nav>
<!--end of navbar-->
