/* Dropdown*/

.dropdown {
   position: relative;
}
.dropdown-menu {
   position: absolute;
   z-index: 30;
   display: none;
   min-width: 160px;
   border-radius: 0.375rem;
   --tw-bg-opacity: 1;
   background-color: rgb(255 255 255 / var(--tw-bg-opacity));
   padding: 0.375rem;
   --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
   --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
   box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
   --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
   --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
   box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
   --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
   --tw-ring-opacity: 0.05;
}
.dropdown-item {
   display: flex;
   height: 2.25rem;
   width: 100%;
   cursor: pointer;
   align-items: center;
   -moz-column-gap: 0.5rem;
   column-gap: 0.5rem;
   white-space: nowrap;
   border-radius: 0.375rem;
   padding-left: 0.75rem;
   padding-right: 0.75rem;
   font-weight: 500;
   --tw-text-opacity: 1;
   color: rgb(75 85 99 / var(--tw-text-opacity));
}
.dropdown-item:hover {
   color: #212b36;
   background-color: rgb(241, 245, 249);
}
.dropdown-item:active {
   color: #212b36;
   background-color: rgb(241, 245, 249);
}

.dropdown-menu.show {
   display: block;
}

.dropdown-menu-end {
   position: absolute !important;
   left: auto !important;
   right: 0 !important;
}

.dropdown-menu-lg {
   min-width: 22rem !important;
   border-radius: 0.5rem;
}

.dropdown-toggle::after {
   display: inline-block;
   margin-left: 0.255em;
   vertical-align: 0.255em;
   content: "";
   border-top: 0.3em solid;
   border-right: 0.3em solid transparent;
   border-bottom: 0;
   border-left: 0.3em solid transparent;
}
