/* Tooltips */

.tooltip {
   background-color: rgb(33, 43, 54);
   border-radius: 0.25rem;
   padding: 0.375rem 0.75rem;
   font-weight: 500;
   color: rgb(255, 255, 255);
}

.tooltip .tooltip-arrow {
   display: block;
   width: 0.8rem;
   height: 0.4rem;
}
.tooltip .tooltip-arrow::before {
   position: absolute;
   content: "";
   border-color: transparent;
   border-style: solid;
}

.bs-tooltip-top .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow {
   bottom: calc(-1 * 0.4rem);
}
.bs-tooltip-top .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before {
   top: -1px;
   border-width: 0.4rem;
   border-top-color: rgb(33, 43, 54);
}

.bs-tooltip-end .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow {
   left: calc(-1 * 0.4rem);
   width: 0.4rem;
   height: 0.8rem;
}
.bs-tooltip-end .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before {
   right: -1px;
   border-width: 0.4rem;
   border-right-color: rgb(33, 43, 54);
}

.bs-tooltip-start .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow {
   right: calc(-1 * 0.4rem);
   width: 0.4rem;
   height: 0.8rem;
}
.bs-tooltip-start .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before {
   left: -1px;
   border-width: 0.4rem;
   border-left-color: rgb(33, 43, 54);
}

.bs-tooltip-bottom .tooltip-arrow,
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow {
   top: calc(-1 * 0.4rem);
}
.bs-tooltip-bottom .tooltip-arrow::before,
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before {
   bottom: -1px;
   border-width: 0 calc(0.8rem * 0.5) 0.4rem;
   border-bottom-color: rgb(33, 43, 54);
}
