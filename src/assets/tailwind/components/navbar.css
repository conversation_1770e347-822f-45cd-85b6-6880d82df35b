/* Navbar */

.navbar-vertical {
   margin-left: -17rem;
   display: block;
   height: 100vh;
   position: fixed;
   top: 0;
   bottom: 0;
   width: 100%;
   max-width: 250px;
   padding: 0rem;
   background-color: rgb(33, 43, 54);
   border-right: 1px solid rgb(33, 43, 54);
   transition: margin 0.25s ease-out;
}

.navbar-vertical .navbar-brand {
   padding: 1rem 1.5rem 1.5rem;

   display: block;
}
.navbar-vertical .navbar-brand img {
   height: 1.875rem;
}
.navbar-vertical .navbar-heading {
   color: rgb(69, 79, 91);
   padding: 1rem 1.5rem;
   font-size: 0.75rem;
   text-transform: uppercase;
   letter-spacing: 0.08rem;
   font-weight: 700;
}
.navbar-vertical .navbar-nav .nav-item .nav-link {
   padding: 0.5rem 1rem;
   display: flex;
   align-items: center;
   color: rgb(145, 158, 171);
   line-height: 1.8;
   -webkit-transition: all 0.5s;
   white-space: nowrap;
   transition: all 0.5s;
   margin: 0rem 1rem;
   border-radius: 0.375rem;
}
.navbar-vertical .navbar-nav .nav-item .nav-link:hover {
   color: rgb(244, 246, 248);
   background-color: rgb(69, 79, 91);
}
.navbar-vertical .navbar-nav .nav-item .nav-link:hover .nav-icon {
   color: rgb(244, 246, 248);
   opacity: 1;
}
.navbar-vertical .navbar-nav .nav-item .nav-link.active {
   color: rgb(244, 246, 248);
   background-color: rgb(69, 79, 91);
}
.navbar-vertical .navbar-nav .nav-item .nav-link.active .nav-icon {
   color: rgb(244, 246, 248);
   opacity: 1;
}
.navbar-vertical .navbar-nav .nav-item .nav-link[data-bs-toggle="collapse"] {
   position: relative;
}
.navbar-vertical .navbar-nav .nav-item .nav-link[data-bs-toggle="collapse"]:after {
   display: block;
   content: "";
   background: url(../fonts/feather-icons/icons/chevron-down.svg);
   font-family: Feather;
   margin-left: auto;
   transition: 0.5s ease;
   position: absolute;
   right: 1.5rem;
   height: 24px;
   width: 24px;
   -webkit-filter: brightness(0) invert(1);
   filter: brightness(0) invert(1);
   -webkit-transform: scale(0.7);
   transform: scale(0.7);
}
.navbar-vertical .navbar-nav .nav-icon {
   opacity: 0.6;
   font-size: 0.875rem;
   transition: all 0.5s;
}
.navbar-vertical .navbar-nav .sub-nav-icon {
   font-size: 0.35rem;
   margin-right: 0.5rem;
}
.navbar-vertical .navbar-nav .nav .nav-item .nav-link {
   padding: 0.25rem 2.5rem;
}

.navbar-vertical .navbar-nav .nav .nav-item .nav .nav-item .nav .nav-item .nav-link {
   padding: 0.25rem 3.5rem;
}

.collapse.in {
   display: block;
}

/* App layout CSS */

#app-layout.toggled .navbar-vertical {
   margin-left: 0;
}

#app-layout.toggled #app-layout-content {
   margin-left: 0;
}

/* Responsive breakpoints */

@media (max-width: 576px) {
   #app-layout-content {
      margin-left: 0rem;
   }
   #app-layout.toggled #app-layout-content {
      margin-left: 15.6875rem;
   }
}
@media (min-width: 768px) {
   .navbar-vertical {
      margin-left: 0rem;
   }

   #app-layout.toggled .navbar-vertical {
      margin-left: -16rem;
   }
}
@media (min-width: 576px) and (max-width: 767.98px) {
   #app-layout-content {
      margin-left: 0rem;
   }
   #app-layout.toggled #app-layout-content {
      margin-left: 15.6875rem;
   }
}

/* simplebar */

.simplebar-scrollbar::before {
   background-color: rgb(33, 43, 54);
}
