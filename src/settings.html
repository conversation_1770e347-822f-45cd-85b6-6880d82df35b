<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="@@webRoot/node_modules/dropzone/dist/dropzone.css" />
   @@include("partials/head.html") @@include("partials/analytics.html")
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <meta name="viewport" content="width=device-width" />
   <meta name="description" content="Settings - TailwindCSS HTML Admin Template Free - Dash UI" />

   <title>Settings - TailwindCSS HTML Admin Template Free - Dash UI</title>
</head>

<body>
   <main>
      <!-- start general setting -->
      <!-- app layout -->
      <div id="app-layout" class="overflow-x-hidden flex">
         @@include("partials/navbar-vertical.html", { "page": "settings", "page_group": "pages" })
         <!-- app layout content -->
         <div id="app-layout-content"
            class="min-h-screen w-full min-w-[100vw] md:min-w-0 ml-[15.625rem] [transition:margin_0.25s_ease-out]">
            @@include("partials/top-navbar.html")
            <div class="p-6">
               <div class="flex items-center mb-4 border-b border-gray-300 pb-4">
                  <!-- title -->
                  <h1 class="inline-block text-xl font-semibold leading-6">General</h1>
               </div>

               <div class="mb-8 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div class="mb-lg-0 col-span-1">
                     <h4 class="mb-1">General Setting</h4>
                     <p class="text-gray-600">Profile configuration settings</p>
                  </div>
                  <!-- card -->
                  <div class="card shadow col-span-3">
                     <!-- card body -->
                     <div class="card-body">
                        <div class="mb-6">
                           <!-- title -->
                           <h4 class="mb-1">General Settings</h4>
                        </div>
                        <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                           <div class="flex-1 text-gray-800 font-semibold">
                              <h5 class="mb-0">Avatar</h5>
                           </div>
                           <div class="flex-[3]">
                              <div class="flex items-center">
                                 <!-- image -->
                                 <div class="me-3">
                                    <img src="assets/images/avatar/avatar-5.jpg" class="rounded-full w-16 h-16"
                                       alt="" />
                                 </div>
                                 <div>
                                    <!-- button -->
                                    <button type="button"
                                       class="btn gap-x-2 bg-white text-gray-800 border-gray-300 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700 active:border-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                       Change
                                    </button>
                                    <!-- button -->
                                    <button type="button"
                                       class="btn gap-x-2 bg-white text-gray-800 border-gray-300 disabled:opacity-50 disabled:pointer-events-none hover:text-white hover:bg-gray-700 hover:border-gray-700 active:bg-gray-700 active:border-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300">
                                       Remove
                                    </button>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- col -->
                        <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                           <div class="flex-1 text-gray-800 font-semibold">
                              <!-- heading -->
                              <h5 class="mb-0">Cover photo</h5>
                           </div>
                           <div class="flex-[3]">
                              <!-- dropzone input -->
                              <div id="my-dropzone"
                                 class="dropzone mt-4 border-dashed border-gray-300 rounded-2 min-h-0"></div>
                           </div>
                        </div>
                        <div>
                           <!-- border -->
                           <div class="mb-6">
                              <h4 class="mb-1">Basic information</h4>
                           </div>
                           <form>
                              <!-- input -->

                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="fullName" class="flex-1 text-gray-800 font-semibold">Full name</label>
                                 <div class="flex-[3] w-full grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="First name" id="fullName" required="" />

                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Last name" id="lastName" required="" />
                                 </div>
                              </div>

                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="email" class="flex-1 text-gray-800 font-semibold">Email</label>
                                 <div class="flex-[3] w-full">
                                    <input type="email"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Email" id="email" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="phone" class="flex-1 text-gray-800 font-semibold">
                                    Phone
                                    <span>(Optional)</span>
                                 </label>
                                 <div class="flex-[3] w-full">
                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Phone" id="phone" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="location" class="flex-1 text-gray-800 font-semibold">Location</label>

                                 <div class="flex-[3] w-full">
                                    <select
                                       class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       id="location">
                                       <option selected="">Select Country</option>
                                       <option value="1">India</option>
                                       <option value="2">UK</option>
                                       <option value="3">USA</option>
                                    </select>
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="addressLine" class="flex-1 text-gray-800 font-semibold">Address line
                                    1</label>

                                 <div class="flex-[3] w-full">
                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="placeholder" id="addressLine" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="addressLineTwo" class="flex-1 text-gray-800 font-semibold">Address line
                                    2</label>
                                 <div class="flex-[3] w-full">
                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="placeholder" id="addressLineTwo" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="zipcode" class="flex-1 text-gray-800 font-semibold">Zip code</label>

                                 <div class="flex-[3]">
                                    <input type="text"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="placeholder" id="zipcode" required="" />
                                 </div>
                              </div>
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <div class="flex-1 text-gray-800 font-semibold"></div>
                                 <div class="flex-[3]">
                                    <button type="submit"
                                       class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                       Save Changes
                                    </button>
                                 </div>
                              </div>
                           </form>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-8 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div class="col-span-1">
                     <h4 class="mb-1">Email Setting</h4>
                     <p class="text-gray-600">Add an email settings to profile</p>
                  </div>
                  <!-- card -->
                  <div class="card shadow col-span-3">
                     <div class="card-body">
                        <div class="mb-6">
                           <h4 class="mb-1">Email</h4>
                        </div>
                        <div>
                           <!-- form -->
                           <form>
                              <!-- input -->
                              <div class="mb-3 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="newEmail" class="flex-1 text-gray-800 font-semibold">New Email</label>
                                 <div class="flex-[3] w-full">
                                    <input type="email"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Enter your email Address" id="newEmail" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <div class="flex-1 text-gray-800 font-semibold"></div>
                                 <div class="flex-[3]">
                                    <button type="submit"
                                       class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                       Save Changes
                                    </button>
                                 </div>
                              </div>
                              <div class="mb-6">
                                 <h4 class="mb-1">Change your password</h4>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="password" class="flex-1 text-gray-800 font-semibold">Current
                                    password</label>
                                 <div class="flex-[3] w-full">
                                    <input type="password"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Enter Current Password" id="password" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="newPassword" class="flex-1 text-gray-800 font-semibold">New
                                    password</label>
                                 <div class="flex-[3] w-full">
                                    <input type="password"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Enter New Password" id="newPassword" required="" />
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="confirmedPassword" class="flex-1 text-gray-800 font-semibold">Confirm new
                                    password</label>
                                 <div class="flex-[3] w-full">
                                    <input type="password"
                                       class="border border-gray-300 text-gray-900 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                       placeholder="Confirm new password" id="confirmedPassword" required="" />
                                 </div>
                              </div>
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <div class="flex-1 text-gray-800 font-semibold"></div>
                                 <div class="flex-[3]">
                                    <div>
                                       <h5 class="mb-1">Password requirements:</h5>
                                       <p>Ensure that these requirements are met:</p>
                                       <!-- list -->
                                       <ul class="list-disc list-inside my-4">
                                          <li>Minimum 8 characters long the more, the better</li>
                                          <li>At least one lowercase character</li>
                                          <li>At least one uppercase character</li>
                                          <li>At least one number, symbol, or whitespace character</li>
                                       </ul>
                                    </div>
                                    <!-- button -->
                                    <button type="submit"
                                       class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                       Save Changes
                                    </button>
                                 </div>
                              </div>
                              <!-- row -->
                           </form>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-8 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div class="col-span-1">
                     <!-- title -->
                     <h4 class="mb-1">Preferences</h4>
                     <p class="text-gray-600">Configure your preferences</p>
                  </div>
                  <div class="card shadow col-span-3">
                     <div class="card-body">
                        <div class="mb-6">
                           <h4 class="mb-1">Preferences</h4>
                        </div>
                        <div>
                           <!-- form -->
                           <form>
                              <!-- select -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="langauge" class="flex-1 text-gray-800 font-semibold">Langauge</label>
                                 <div class="flex-[3] w-full">
                                    <select id="langauge"
                                       class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none">
                                       <option selected="">English</option>
                                       <option value="Hindi">Hindi</option>
                                       <option value="Spanish">Spanish</option>
                                       <option value="Arabic">Arabic</option>
                                    </select>
                                 </div>
                              </div>

                              <!-- select -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="timeZone" class="flex-1 text-gray-800 font-semibold">Date Format</label>
                                 <div class="flex-[3] w-full">
                                    <select id="timeZone"
                                       class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none">
                                       <option selected="">GMT +5.30</option>
                                       <option value="1">GMT +5.30</option>
                                       <option value="2">GMT +5.30</option>
                                       <option value="3">GMT +5.30</option>
                                    </select>
                                 </div>
                              </div>

                              <!-- select -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="dateFormat" class="flex-1 text-gray-800 font-semibold">Date Format</label>
                                 <div class="flex-[3] w-full">
                                    <select id="dateFormat"
                                       class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none">
                                       <option selected="">No Preference</option>
                                       <option value="Preference">Preference</option>
                                    </select>
                                 </div>
                              </div>
                              <!-- input -->
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <label for="confirmedPassword"
                                    class="flex-1 text-gray-800 font-semibold">Default</label>
                                 <div class="flex-[3] w-full flex gap-5">
                                    <div>
                                       <input
                                          class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded-full focus:ring-indigo-600 focus:outline-none focus:ring-2 checked:bg-indigo-600"
                                          type="radio" checked="" name="radio-direct" value="1" id="radio-option-1" />
                                       <label for="radio-option-1" class="ml-1 text-slate-600">On</label>
                                    </div>
                                    <!-- input -->
                                    <div>
                                       <input
                                          class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded-full focus:ring-indigo-600 focus:outline-none focus:ring-2 checked:bg-indigo-600"
                                          type="radio" name="radio-direct" value="2" id="radio-option-2" />
                                       <label for="radio-option-2" class="ml-1 text-slate-600">Off</label>
                                    </div>
                                 </div>
                              </div>
                              <!-- form -->
                              <div class="mb-6 inline-flex md:flex gap-3 flex-col md:flex-row w-full">
                                 <label for="confirmedPassword" class="flex-1 text-gray-800 font-semibold">Choose option
                                    default</label>
                                 <div class="flex-[3] w-full">
                                    <div class="flex items-center mb-2">
                                       <input
                                          class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                          type="checkbox" id="default-checkbox" />
                                       <label for="default-checkbox" class="ml-1 text-slate-600">Tell me</label>
                                    </div>
                                    <div class="flex items-center mb-2">
                                       <input
                                          class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                          type="checkbox" id="default-checkbox-2" />
                                       <label for="default-checkbox-2" class="ml-1 text-slate-600">Open e-mail</label>
                                    </div>
                                    <div class="flex items-center">
                                       <input
                                          class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                          type="checkbox" id="default-checkbox-3-1" />
                                       <label for="default-checkbox-3-1" class="ml-1 text-slate-600">Show
                                          default</label>
                                    </div>
                                 </div>
                              </div>
                              <div class="mb-6 inline-flex md:flex md:items-center gap-3 flex-col md:flex-row w-full">
                                 <div class="flex-1 text-gray-800 font-semibold"></div>
                                 <div class="flex-[3]">
                                    <!-- button -->
                                    <button type="submit"
                                       class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                       Save Changes
                                    </button>
                                 </div>
                              </div>
                           </form>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-8 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div class="col-span-1">
                     <h4 class="mb-1">Notifications</h4>
                     <p class="text-gray-600">Change notification settings</p>
                  </div>
                  <!-- card -->
                  <div class="card shadow col-span-3">
                     <div class="card-body">
                        <div class="mb-6">
                           <h4 class="mb-1">Notification for email, mobile & Slack</h4>
                           <p>To start using Slack for personal notifications, you should first connect Slack.</p>
                        </div>

                        <div>
                           <!-- table -->
                           <div class="relative overflow-x-auto">
                              <table class="text-left w-full">
                                 <thead class="text-gray-700 whitespace-nowrap">
                                    <tr>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">Activity &amp;
                                          Conversation</th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-smartphone">
                                             <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                                             <line x1="12" y1="18" x2="12.01" y2="18"></line>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-slack">
                                             <path
                                                d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z">
                                             </path>
                                             <path
                                                d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z">
                                             </path>
                                             <path
                                                d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z">
                                             </path>
                                             <path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z">
                                             </path>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-mail">
                                             <path
                                                d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                             </path>
                                             <polyline points="22,6 12,13 2,6"></polyline>
                                          </svg>
                                       </th>
                                    </tr>
                                 </thead>
                                 <tbody>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left"></td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div>
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-3" />
                                             <label for="default-checkbox-3" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div>
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-4" />
                                             <label for="default-checkbox-4" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-4-1" />
                                             <label for="default-checkbox-4-1" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When a Files is shared with
                                          a team</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-5" />
                                             <label for="default-checkbox-5" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-minus icon-sm">
                                             <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-6" />
                                             <label for="default-checkbox-6" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>

                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone requests access
                                          to my design</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-7" />
                                             <label for="default-checkbox-7" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-8" />
                                             <label for="default-checkbox-8" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-9" />
                                             <label for="default-checkbox-9" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone comments in
                                          threads I’m following</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-10" />
                                             <label for="default-checkbox-10" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-11" />
                                             <label for="default-checkbox-11" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-12" />
                                             <label for="default-checkbox-12" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone @mentions me in
                                          any comments</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-13" />
                                             <label for="default-checkbox-13" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-minus icon-sm">
                                             <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-minus icon-sm">
                                             <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                       </td>
                                    </tr>
                                 </tbody>
                              </table>
                           </div>
                           <div class="relative overflow-x-auto mt-6">
                              <!-- table -->
                              <table class="text-left w-full">
                                 <thead class="text-gray-700 whitespace-nowrap">
                                    <tr>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">Project activity</th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-smartphone me-2 icon-sm">
                                             <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                                             <line x1="12" y1="18" x2="12.01" y2="18"></line>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-slack me-2 icon-sm">
                                             <path
                                                d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z">
                                             </path>
                                             <path
                                                d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z">
                                             </path>
                                             <path
                                                d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z">
                                             </path>
                                             <path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z">
                                             </path>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-mail me-2 icon-sm">
                                             <path
                                                d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                             </path>
                                             <polyline points="22,6 12,13 2,6"></polyline>
                                          </svg>
                                       </th>
                                    </tr>
                                 </thead>
                                 <tbody>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone adds me to a
                                          project</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-14" />
                                             <label for="default-checkbox-14" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-minus icon-sm">
                                             <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-15" />
                                             <label for="default-checkbox-15" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                 </tbody>
                              </table>
                           </div>
                           <div class="relative overflow-x-auto mt-6 mb-8">
                              <!-- table -->
                              <table class="text-left w-full">
                                 <thead class="text-gray-700 whitespace-nowrap">
                                    <tr>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">Team activity</th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-smartphone me-2 icon-sm">
                                             <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                                             <line x1="12" y1="18" x2="12.01" y2="18"></line>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-slack me-2 icon-sm">
                                             <path
                                                d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z">
                                             </path>
                                             <path
                                                d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z">
                                             </path>
                                             <path
                                                d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z">
                                             </path>
                                             <path
                                                d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z">
                                             </path>
                                             <path
                                                d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z">
                                             </path>
                                             <path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z">
                                             </path>
                                          </svg>
                                       </th>
                                       <th scope="col" class="border-b bg-gray-100 px-6 py-3">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-mail me-2 icon-sm">
                                             <path
                                                d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                             </path>
                                             <polyline points="22,6 12,13 2,6"></polyline>
                                          </svg>
                                       </th>
                                    </tr>
                                 </thead>
                                 <tbody>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When my invitees sign up
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-17" />
                                             <label for="default-checkbox-17" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-18" />
                                             <label for="default-checkbox-18" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-19" />
                                             <label for="default-checkbox-19" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone requests access
                                          to my team</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-20" />
                                             <label for="default-checkbox-20" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                             stroke-linecap="round" stroke-linejoin="round"
                                             class="feather feather-minus icon-sm">
                                             <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-21" />
                                             <label for="default-checkbox-21" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>

                                    <tr>
                                       <td class="border-b font-medium py-3 pl-6 text-left">When someone invites me to a
                                          team</td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-22" />
                                             <label for="default-checkbox-22" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-23" />
                                             <label for="default-checkbox-23" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                       <td class="border-b font-medium py-3 pl-6 text-left">
                                          <div class="form-check">
                                             <input
                                                class="w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-600 focus:outline-none focus:ring-2"
                                                type="checkbox" id="default-checkbox-24" />
                                             <label for="default-checkbox-24" class="ml-1 text-slate-600"></label>
                                          </div>
                                       </td>
                                    </tr>
                                 </tbody>
                              </table>
                           </div>
                           <div class="mb-4 inline-flex md:flex gap-3 flex-col md:flex-row w-full">
                              <!-- select -->
                              <div class="flex-1 flex flex-col gap-3">
                                 <label for="notification">When should we send you notifications?</label>
                                 <select
                                    class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                    id="notification">
                                    <option selected="">Always</option>
                                    <option value="1">One</option>
                                    <option value="2">Two</option>
                                    <option value="3">Three</option>
                                 </select>
                              </div>
                              <div class="flex-1 flex flex-col gap-3">
                                 <!-- select -->
                                 <label for="dailyDigest">Daily Digest</label>
                                 <select
                                    class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                    id="dailyDigest">
                                    <option selected="">Everyday</option>
                                    <option value="1">One</option>
                                    <option value="2">Two</option>
                                    <option value="3">Three</option>
                                 </select>
                              </div>
                              <div class="flex-1 flex flex-col gap-3">
                                 <!-- select -->
                                 <label for="time">Time</label>
                                 <select
                                    class="text-base border border-gray-300 rounded focus:ring-indigo-600 focus:border-indigo-600 block w-full p-2 px-3 disabled:opacity-50 disabled:pointer-events-none"
                                    id="time">
                                    <option selected="">2PM</option>
                                    <option value="1">One</option>
                                    <option value="2">Two</option>
                                    <option value="3">Three</option>
                                 </select>
                              </div>
                           </div>
                           <!-- button -->
                           <div class="flex-1 text-gray-800 font-semibold">
                              <button type="submit"
                                 class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                                 Save Changes
                              </button>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mb-8 grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div class="col-span-1">
                     <!-- content -->
                     <h4 class="mb-1">Delete Account</h4>
                     <p class="text-gray-600">Easily set up social media accounts</p>
                  </div>
                  <!-- card -->
                  <div class="card shadow col-span-3">
                     <!-- card body -->
                     <div class="card-body">
                        <h4 class="mb-1">Danger Zone</h4>
                        <p class="mb-4">Delete any and all content you have, such as articles, comments, your reading
                           list or chat messages. Allow your username to become available to anyone.</p>
                        <button type="submit"
                           class="btn bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-800 hover:border-indigo-800 active:bg-indigo-800 active:border-indigo-800 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                           Delete Account
                        </button>
                        <p class="mb-0 mt-3">
                           Feel free to contact with any
                           <a href="#" class="text-indigo-600"><EMAIL></a>
                           questions.
                        </p>
                     </div>

                     <div>
                        <!-- table -->
                     </div>
                  </div>
               </div>
            </div>
            @@include("partials/footer.html")
         </div>
      </div>
      <!-- end of general setting -->
   </main>
   <script src="@@webRoot/node_modules/dropzone/dist/dropzone-min.js"></script>
   @@include("partials/buy-template.html")
   @@include("partials/scripts.html")
</body>

</html>