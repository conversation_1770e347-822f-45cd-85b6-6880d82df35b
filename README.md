# [Dash UI - TailwindCSS HTML Admin Template Free](https://dashui.codescandy.com/) [![Twitter URL](https://img.shields.io/twitter/url?style=social&url=https%3A%2F%2Fgithub.com%2Fcodescandy%2FDash-UI)](https://twitter.com/intent/tweet?text=Dash%20-%20UI%20Bootstrap%205%20Theme&url=https%3A%2F%2Fgithub.com%2Fcodescandy%2FDash-UI&via=getcodescandy)

<a href="https://dashui.codescandy.com/">
 <img src="https://github.com/codescandy/dashui-tailwindcss/blob/main/src/assets/images/marketing/hero-img.jpg" alt="Dash UI - TailwindCSS HTML Admin Template Free"/>
</a>
 <br />
<br />

![Static Badge](https://img.shields.io/badge/tags-v1.0.0-blue) ![Static Badge](https://img.shields.io/badge/License-MIT-blue) ![Static Badge](https://img.shields.io/badge/issue-0%20open-green) ![Static Badge](https://img.shields.io/badge/forks-1-blue) ![Static Badge](https://img.shields.io/badge/starts-2-blue)

Dash-UI is a TailwindCSS Admin & Dashboard Theme. Dash UI Kit a free & Pro templates kit fully coded with TailwindCSS.

## Workflow

This product is built using the following widely used technologies:

-  Most popular CSS Framework [TailwindCSS](https://tailwindcss.com/)
-  Productive workflow tool Gulp
-  Figma File [Demo](https://www.figma.com/community/file/1259105309122518026/dash-ui-admin-dashboard-template)

## Table of Contents

-  [Quick Start](#quick-start)
-  [Documentation](#documentation)
-  [File Structure](#file-structure)
-  [Browser Support](#browser-support)
-  [Technical Support or Questions](#technical-support-or-questions)

## Quick start

1. Download from clone this repository
2. Download the project's zip
3. Make sure you have Node locally installed.
4. Download Gulp Command Line Interface to be able to use gulp in your Terminal.

```
npm install gulp-cli -g
```

5. After installing Gulp, run npm install in the main `dash-ui/` folder to download all the project dependencies. You'll find them in the `node_modules/` folder.

```
npm install
```

6. Run gulp in the `dash-ui/` folder to serve the project files using BrowserSync. Running gulp will compile the theme and open `/index.html` in your main browser.

```
gulp
```

7. Generates a /dist directory with all the production files.

```
gulp build
```

## Documentation

The documentation for Dash UI is provide on our [website](https://dashui.codescandy.com/tailwindcss/docs.html).

## File Structure

Within the download you'll find the following directories and files:

```

Dash UI TailwindCSS Theme

├── src
│   ├── assets                       # The output css directory
│       ├── css                      # Compiled CSS
│       ├── fonts                    # All fonts are used in the theme.
│       ├── images                   # All the images are used in the theme
|       ├── js                       # All Javascript source files used in theme.
│       ├── tailwind                 # CSS files
│   ├── components                   # All Components for theme.
│   ├── partials                     # A specific loop header and footer files for the templating.
│   ├── index.html                   # Index and All HTML file is start file run when the gulp
├── .gitignore                       # Ignore file for git
├── gulpfile.js                      # Gulp setup file
├── package.json
├── package.lock.json
├── README.md
└── tailwind.config.js               # TailwindCSS configuration file


```

## Browser Support

At present, we officially aim to support the last two versions of the following browsers:

<div class="flex">
<img src="https://github.com/codescandy/Dash-UI/blob/main/src/assets/images/marketing/chrome.png" width="64" height="64">
<img src="https://github.com/codescandy/Dash-UI/blob/main/src/assets/images/marketing/firefox.png" width="64" height="64">
<img src="https://github.com/codescandy/Dash-UI/blob/main/src/assets/images/marketing/edge.png" width="64" height="64">
<img src="https://github.com/codescandy/Dash-UI/blob/main/src/assets/images/marketing/safari.png" width="64" height="64">
<img src="https://github.com/codescandy/Dash-UI/blob/main/src/assets/images/marketing/opera.png" width="64" height="64">
</div>

## Technical Support or Questions

If you have questions or need help integrating the product please [contact us](https://codescandy.com/contact-us/). [Support](https://github.com/codescandy/Dash-UI/discussions)

## Useful Links

-  [Bootstrap Template](https://dashui.codescandy.com/free-bootstrap-5-admin-dashboard-template.html)
-  [React Template](https://dashui.codescandy.com/free-reactjs-admin-dashboard-template.html)
-  [Next.js Template](https://dashui.codescandy.com/free-next-js-admin-dashboard-template.html)
-  [Nuxt.js Template](https://dashui.codescandy.com/free-nuxt-js-admin-dashboard-template.html)
-  [Tailwind Template](https://dashui.codescandy.com/free-tailwindcss-admin-dashboard-html-template.html)

## Figma Design File

Ready to use [Figma File](https://www.figma.com/community/file/1259105309122518026/dash-ui-admin-dashboard-template).

## Upgrade to PRO

Get more power with [Dash UI pro](https://dashui.codescandy.com/) featuring bunch of UI components, forms, tables, charts, pages, and icons.

| Free Version                                                                                       | DashUI PRO                                                                                  |
| -------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------- |
| ✔️ 1 Dashboard                                                                                     | ✔️ 6 Dashboard                                                                              |
| ✔️ 11 HTML Pages                                                                                   | ✔️ 60+ HTML pages                                                                           |
| ✔️ Includine Docs                                                                                  | ✔️ Documentation                                                                            |
| ✔️ 4 Plugins                                                                                       | ✔️ 10+ Plugins                                                                              |
| ✔️ Source Files                                                                                    | ✔️ Source Files                                                                             |
| ❌ Dark Mode                                                                                       | ✔️ Dark Mode                                                                                |
| ❌ Layout Options                                                                                  | ✔️ Layout Variations                                                                        |
| ❌ Priority Support                                                                                | ✔️ Priority Support                                                                         |
| -                                                                                                  | ✔️ Free Update                                                                              |
| [Free Download](https://dashui.codescandy.com/free-tailwindcss-admin-dashboard-html-template.html) | [Get Dash UI](https://dashui.codescandy.com/tailwindcss-admin-dashboard-html-template.html) |
